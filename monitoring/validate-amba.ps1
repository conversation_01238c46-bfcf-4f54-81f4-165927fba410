# Validate AMBA template before deployment
$timestamp = Get-Date -Format 'yyyyMMdd-HHmmss'

Write-Host "Creating AMBA parameter file for validation..." -ForegroundColor Cyan

# Create minimal parameter file
$params = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    'contentVersion' = "*******"
    'parameters' = @{
        'enterpriseScaleCompanyPrefix' = @{ 'value' = "test-ewh" }
        'platformManagementGroup' = @{ 'value' = "test-ewh-platform" }
        'IdentityManagementGroup' = @{ 'value' = "test-ewh-identity" }
        'managementManagementGroup' = @{ 'value' = "test-ewh-management" }
        'connectivityManagementGroup' = @{ 'value' = "test-ewh-connectivity" }
        'LandingZoneManagementGroup' = @{ 'value' = "test-ewh-landingzones" }
        'enableAMBAConnectivity' = @{ 'value' = "Yes" }
        'enableAMBAIdentity' = @{ 'value' = "Yes" }
        'enableAMBAManagement' = @{ 'value' = "Yes" }
        'enableAMBAServiceHealth' = @{ 'value' = "Yes" }
        'enableAMBANotificationAssets' = @{ 'value' = "Yes" }
        'managementSubscriptionId' = @{ 'value' = "864282bd-af70-4198-9af5-2ffd74bd9b52" }
        'ALZMonitorResourceGroupName' = @{ 'value' = "rg-amba-monitoring-001" }
        'ALZMonitorResourceGroupLocation' = @{ 'value' = "East US" }
        'ALZMonitorActionGroupEmail' = @{ 'value' = @("<EMAIL>") }
        'telemetryOptOut' = @{ 'value' = "No" }
        'bringYourOwnUserAssignedManagedIdentity' = @{ 'value' = "No" }
        'userAssignedManagedIdentityName' = @{ 'value' = "id-amba-test-ewh-001" }
        'ALZLogicappResourceId' = @{ 'value' = "" }
        'ALZLogicappCallbackUrl' = @{ 'value' = "" }
        'ALZArmRoleId' = @{ 'value' = @() }
        'ALZEventHubResourceId' = @{ 'value' = @() }
        'ALZWebhookServiceUri' = @{ 'value' = @() }
        'ALZFunctionResourceId' = @{ 'value' = "" }
        'ALZFunctionTriggerUrl' = @{ 'value' = "" }
        'BYOActionGroup' = @{ 'value' = @() }
        'BYOAlertProcessingRule' = @{ 'value' = @() }
    }
}

$paramFile = "monitoring/amba-validate-$timestamp.json"
$params | ConvertTo-Json -Depth 10 | Set-Content $paramFile

Write-Host "Parameter file created: $paramFile" -ForegroundColor Green

# Validate template
Write-Host "Validating AMBA template..." -ForegroundColor Yellow

$templateUri = "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json"

Write-Host "Template: $templateUri" -ForegroundColor Blue
Write-Host "Management Group: test-ewh" -ForegroundColor Blue

# Run validation
Write-Host "Running validation..." -ForegroundColor Cyan
$validationResult = az deployment mg validate `
    --template-uri $templateUri `
    --location "East US" `
    --management-group-id "test-ewh" `
    --parameters "@$paramFile" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Template validation successful!" -ForegroundColor Green
    Write-Host "Template is ready for deployment" -ForegroundColor Green
    
    # Show parameter file content for reference
    Write-Host "`nParameter file content:" -ForegroundColor Cyan
    Get-Content $paramFile | Write-Host -ForegroundColor Gray
    
} else {
    Write-Host "❌ Template validation failed!" -ForegroundColor Red
    Write-Host "Validation errors:" -ForegroundColor Yellow
    Write-Host $validationResult -ForegroundColor Red
}

# Clean up
Remove-Item $paramFile -Force -ErrorAction SilentlyContinue
Write-Host "`nCleaned up parameter file" -ForegroundColor Gray
