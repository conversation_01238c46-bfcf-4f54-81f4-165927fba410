# Manual AMBA Deployment Instructions

## ✅ **Đã sửa lỗi parameter types!**

Tất cả các lỗi về parameter types đã được sửa:
- ✅ `ALZEventHubResourceId`: String → Array
- ✅ `BYOActionGroup`: String → Array  
- ✅ `BYOAlertProcessingRule`: String → Array
- ✅ `ALZWebhookServiceUri`: Array (đã đúng)
- ✅ `ALZArmRoleId`: Array (đã đúng)

## 🎯 **Manual Deployment Steps**

### Step 1: Login to Azure
```bash
az login
az account set --subscription "864282bd-af70-4198-9af5-2ffd74bd9b52"
```

### Step 2: Validate Template (Optional)
```bash
az deployment mg validate \
  --template-uri "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json" \
  --location "eastus" \
  --management-group-id "test-ewh" \
  --parameters "@monitoring/deploy-amba-manual.json"
```

### Step 3: Deploy AMBA
```bash
az deployment mg create \
  --name "AMBA-Manual-$(date +%Y%m%d-%H%M%S)" \
  --template-uri "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json" \
  --location "eastus" \
  --management-group-id "test-ewh" \
  --parameters "@monitoring/deploy-amba-manual.json"
```

### Step 4: Verify Deployment
```bash
# Check policy assignments
az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh --query "[?contains(displayName, 'ALZ')].{Name:name, DisplayName:displayName}" -o table

# Check monitoring resource group
az group show --name "rg-amba-monitoring-001" --query "{Name:name, Location:location, ProvisioningState:properties.provisioningState}" -o table

# Check policy compliance
az policy state list --management-group test-ewh --query "[?contains(policyDefinitionName, 'ALZ')].{Policy:policyDefinitionName, Compliance:complianceState}" -o table
```

## 📋 **What Will Be Deployed**

### Policy Definitions (100+ policies)
- VM monitoring policies
- Storage account monitoring
- Network monitoring
- Key Vault monitoring
- Service Health monitoring
- And many more...

### Policy Initiatives (Grouped Policies)
- **Connectivity Initiative** → `test-ewh-connectivity`
- **Identity Initiative** → `test-ewh-identity`
- **Management Initiative** → `test-ewh-management`
- **Landing Zone Initiative** → `test-ewh-landingzones`
- **Service Health Initiative** → `test-ewh`

### Resources
- **Resource Group**: `rg-amba-monitoring-001`
- **Action Group**: For email notifications
- **User Assigned Managed Identity**: `id-amba-test-ewh-001`

## 🎯 **Expected Results**

After successful deployment:

1. **Policy Assignments**: Policies will be assigned to management groups
2. **Automatic Alerts**: New resources will automatically get monitoring alerts
3. **Email Notifications**: Alerts will be sent to `<EMAIL>`
4. **Compliance Tracking**: Policy compliance can be monitored

## 🚀 **Testing Automatic Alerts**

Deploy a test VM to verify automatic alert creation:

```bash
# Deploy test VM
az vm create \
  --resource-group "test-rg" \
  --name "test-vm" \
  --image "Ubuntu2204" \
  --admin-username "azureuser" \
  --generate-ssh-keys

# Check if alerts were created automatically
az monitor metrics alert list --resource-group "test-rg" -o table
```

## 🔧 **Troubleshooting**

### Common Issues:

1. **Management Group Not Found**
   - Verify management group exists: `az account management-group list`
   - Use correct management group ID

2. **Permission Denied**
   - Ensure you have Owner/Contributor role on management group
   - Check subscription permissions

3. **Template Validation Errors**
   - All parameter types have been fixed
   - Use the provided `deploy-amba-manual.json` file

4. **Deployment Timeout**
   - AMBA deployment can take 10-15 minutes
   - Monitor progress in Azure Portal

## ✅ **Success Indicators**

Deployment is successful when:
- ✅ Deployment status shows "Succeeded"
- ✅ Policy assignments appear in management groups
- ✅ Resource group `rg-amba-monitoring-001` is created
- ✅ Action group is configured with email notifications
- ✅ New resources automatically get monitoring alerts

## 📚 **Next Steps**

1. **Configure Data Collection Rules**
2. **Set up Log Analytics Workspace forwarding**
3. **Create custom dashboards**
4. **Configure additional notification channels**
5. **Run remediation tasks for existing resources**
