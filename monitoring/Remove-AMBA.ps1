#Requires -<PERSON><PERSON>les <PERSON>cco<PERSON>, Az.Profile, Az.Resources

<#
.SYNOPSIS
    Remove Azure Monitor Baseline Alerts (AMBA) deployment for EWH Landing Zone

.DESCRIPTION
    This script removes AMBA policies and assignments from the specified management group.
    It removes policy assignments, policy set definitions, and policy definitions in the correct order.

.PARAMETER enterpriseScaleCompanyPrefix
    Provide the company prefix for the management group hierarchy (max 10 characters)

.PARAMETER whatIf
    Perform a what-if operation to preview what would be removed without actually removing

.PARAMETER force
    Force removal without confirmation prompts

.EXAMPLE
    .\Remove-AMBA.ps1 -enterpriseScaleCompanyPrefix "EWH"

.EXAMPLE
    .\Remove-AMBA.ps1 -enterpriseScaleCompanyPrefix "EWH" -whatIf

.EXAMPLE
    .\Remove-AMBA.ps1 -enterpriseScaleCompanyPrefix "EWH" -force

.NOTES
    Author: EWH IT Team
    Version: 1.0.0
    Requires: Az PowerShell modules (Az.Accounts, Az.Profile, Az.Resources)
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$enterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [switch]$whatIf,

    [Parameter(Mandatory = $false)]
    [switch]$force
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::Cyan
        "Magenta" = [ConsoleColor]::Magenta
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

# Function to check if user is logged in to Azure
function Test-AzureLogin {
    try {
        $context = Get-AzContext
        if ($null -eq $context) {
            return $false
        }
        return $true
    }
    catch {
        return $false
    }
}

# Main execution
try {
    Write-ColorOutput "Starting Azure Monitor Baseline Alerts (AMBA) removal..." "Cyan"
    
    # Check if user is logged in to Azure
    if (-not (Test-AzureLogin)) {
        Write-ColorOutput "You are not logged in to Azure. Please run 'Connect-AzAccount' first." "Red"
        exit 1
    }
    
    # Get current Azure context
    $context = Get-AzContext
    Write-ColorOutput "Current Azure context: $($context.Account.Id)" "Green"
    
    # Warning message
    if (-not $force -and -not $whatIf) {
        Write-ColorOutput "`n⚠️  WARNING: This will remove all AMBA policies and assignments from management group '$enterpriseScaleCompanyPrefix'" "Yellow"
        $confirmation = Read-Host "Are you sure you want to continue? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-ColorOutput "Operation cancelled by user." "Yellow"
            exit 0
        }
    }
    
    $removalResults = @()
    
    # Step 1: Remove Policy Assignment
    Write-ColorOutput "`n=== Removing AMBA Policy Assignment ===" "Yellow"
    
    try {
        $assignment = Get-AzPolicyAssignment -Scope "/providers/Microsoft.Management/managementGroups/$enterpriseScaleCompanyPrefix" | Where-Object { $_.Name -eq "EWH-AMBA-Assignment" }
        if ($assignment) {
            if ($whatIf) {
                Write-ColorOutput "Would remove Policy Assignment: EWH-AMBA-Assignment" "Cyan"
            }
            else {
                Remove-AzPolicyAssignment -Id $assignment.ResourceId -Confirm:$false
                Write-ColorOutput "✅ Removed Policy Assignment: EWH-AMBA-Assignment" "Green"
            }
            $removalResults += [PSCustomObject]@{
                Type = "Policy Assignment"
                Name = "EWH-AMBA-Assignment"
                Status = if ($whatIf) { "WOULD REMOVE" } else { "REMOVED" }
            }
        }
        else {
            Write-ColorOutput "Policy Assignment 'EWH-AMBA-Assignment' not found (may already be removed)" "Yellow"
            $removalResults += [PSCustomObject]@{
                Type = "Policy Assignment"
                Name = "EWH-AMBA-Assignment"
                Status = "NOT FOUND"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error removing Policy Assignment: $($_.Exception.Message)" "Red"
        $removalResults += [PSCustomObject]@{
            Type = "Policy Assignment"
            Name = "EWH-AMBA-Assignment"
            Status = "ERROR"
        }
    }
    
    # Step 2: Remove Policy Set Definition (Initiative)
    Write-ColorOutput "`n=== Removing AMBA Policy Set Definition ===" "Yellow"
    
    try {
        $initiative = Get-AzPolicySetDefinition -ManagementGroupName $enterpriseScaleCompanyPrefix -Custom | Where-Object { $_.Name -eq "EWH-MonitoringInitiative" }
        if ($initiative) {
            if ($whatIf) {
                Write-ColorOutput "Would remove Policy Set Definition: EWH-MonitoringInitiative" "Cyan"
            }
            else {
                Remove-AzPolicySetDefinition -Id $initiative.ResourceId -Force
                Write-ColorOutput "✅ Removed Policy Set Definition: EWH-MonitoringInitiative" "Green"
            }
            $removalResults += [PSCustomObject]@{
                Type = "Policy Set Definition"
                Name = "EWH-MonitoringInitiative"
                Status = if ($whatIf) { "WOULD REMOVE" } else { "REMOVED" }
            }
        }
        else {
            Write-ColorOutput "Policy Set Definition 'EWH-MonitoringInitiative' not found (may already be removed)" "Yellow"
            $removalResults += [PSCustomObject]@{
                Type = "Policy Set Definition"
                Name = "EWH-MonitoringInitiative"
                Status = "NOT FOUND"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error removing Policy Set Definition: $($_.Exception.Message)" "Red"
        $removalResults += [PSCustomObject]@{
            Type = "Policy Set Definition"
            Name = "EWH-MonitoringInitiative"
            Status = "ERROR"
        }
    }
    
    # Step 3: Remove Policy Definitions
    Write-ColorOutput "`n=== Removing AMBA Policy Definitions ===" "Yellow"
    
    $expectedPolicies = @(
        "EWH-ServiceHealthAlerts",
        "EWH-ResourceHealthAlerts",
        "EWH-ActivityLogAlerts",
        "EWH-VMMetricAlerts"
    )
    
    foreach ($policyName in $expectedPolicies) {
        try {
            $policy = Get-AzPolicyDefinition -ManagementGroupName $enterpriseScaleCompanyPrefix -Custom | Where-Object { $_.Name -eq $policyName }
            if ($policy) {
                if ($whatIf) {
                    Write-ColorOutput "Would remove Policy Definition: $policyName" "Cyan"
                }
                else {
                    Remove-AzPolicyDefinition -Id $policy.ResourceId -Force
                    Write-ColorOutput "✅ Removed Policy Definition: $policyName" "Green"
                }
                $removalResults += [PSCustomObject]@{
                    Type = "Policy Definition"
                    Name = $policyName
                    Status = if ($whatIf) { "WOULD REMOVE" } else { "REMOVED" }
                }
            }
            else {
                Write-ColorOutput "Policy Definition '$policyName' not found (may already be removed)" "Yellow"
                $removalResults += [PSCustomObject]@{
                    Type = "Policy Definition"
                    Name = $policyName
                    Status = "NOT FOUND"
                }
            }
        }
        catch {
            Write-ColorOutput "❌ Error removing Policy Definition '$policyName': $($_.Exception.Message)" "Red"
            $removalResults += [PSCustomObject]@{
                Type = "Policy Definition"
                Name = $policyName
                Status = "ERROR"
            }
        }
    }
    
    # Removal Summary
    Write-ColorOutput "`n=== Removal Summary ===" "Cyan"
    
    $removedCount = ($removalResults | Where-Object { $_.Status -eq "REMOVED" }).Count
    $wouldRemoveCount = ($removalResults | Where-Object { $_.Status -eq "WOULD REMOVE" }).Count
    $notFoundCount = ($removalResults | Where-Object { $_.Status -eq "NOT FOUND" }).Count
    $errorCount = ($removalResults | Where-Object { $_.Status -eq "ERROR" }).Count
    $totalCount = $removalResults.Count
    
    Write-ColorOutput "Total Items: $totalCount" "White"
    if ($whatIf) {
        Write-ColorOutput "Would Remove: $wouldRemoveCount" "Cyan"
    }
    else {
        Write-ColorOutput "Removed: $removedCount" "Green"
    }
    Write-ColorOutput "Not Found: $notFoundCount" "Yellow"
    Write-ColorOutput "Errors: $errorCount" "Red"
    
    # Display detailed results
    Write-ColorOutput "`n=== Detailed Results ===" "Cyan"
    $removalResults | Format-Table -AutoSize
    
    # Overall result
    if ($whatIf) {
        Write-ColorOutput "`n📋 What-If operation completed. No actual changes were made." "Cyan"
    }
    elseif ($errorCount -eq 0) {
        Write-ColorOutput "`n🎉 AMBA removal completed successfully!" "Green"
    }
    else {
        Write-ColorOutput "`n⚠️  AMBA removal completed with some errors. Please review." "Yellow"
    }
}
catch {
    Write-ColorOutput "Error during AMBA removal: $($_.Exception.Message)" "Red"
    Write-ColorOutput "Stack Trace: $($_.ScriptStackTrace)" "Red"
    exit 1
}

Write-ColorOutput "AMBA removal script completed." "Cyan"
