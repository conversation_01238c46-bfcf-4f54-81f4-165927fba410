# Test AMBA deployment with fixed parameters
$timestamp = Get-Date -Format 'yyyyMMdd-HHmmss'

Write-Host "Testing AMBA deployment with fixed parameters..." -ForegroundColor Cyan

# Create parameter file with proper location format
$params = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    'contentVersion' = "*******"
    'parameters' = @{
        'enterpriseScaleCompanyPrefix' = @{ 'value' = "test-ewh" }
        'platformManagementGroup' = @{ 'value' = "test-ewh-platform" }
        'IdentityManagementGroup' = @{ 'value' = "test-ewh-identity" }
        'managementManagementGroup' = @{ 'value' = "test-ewh-management" }
        'connectivityManagementGroup' = @{ 'value' = "test-ewh-connectivity" }
        'LandingZoneManagementGroup' = @{ 'value' = "test-ewh-landingzones" }
        'enableAMBAConnectivity' = @{ 'value' = "Yes" }
        'enableAMBAIdentity' = @{ 'value' = "Yes" }
        'enableAMBAManagement' = @{ 'value' = "Yes" }
        'enableAMBAServiceHealth' = @{ 'value' = "Yes" }
        'enableAMBANotificationAssets' = @{ 'value' = "Yes" }
        'managementSubscriptionId' = @{ 'value' = "864282bd-af70-4198-9af5-2ffd74bd9b52" }
        'ALZMonitorResourceGroupName' = @{ 'value' = "rg-amba-monitoring-001" }
        'ALZMonitorResourceGroupLocation' = @{ 'value' = "eastus" }
        'ALZMonitorActionGroupEmail' = @{ 'value' = @("<EMAIL>") }
        'telemetryOptOut' = @{ 'value' = "No" }
        'bringYourOwnUserAssignedManagedIdentity' = @{ 'value' = "No" }
        'userAssignedManagedIdentityName' = @{ 'value' = "id-amba-test-ewh-001" }
        'ALZLogicappResourceId' = @{ 'value' = "" }
        'ALZLogicappCallbackUrl' = @{ 'value' = "" }
        'ALZArmRoleId' = @{ 'value' = @() }
        'ALZEventHubResourceId' = @{ 'value' = @() }
        'ALZWebhookServiceUri' = @{ 'value' = @() }
        'ALZFunctionResourceId' = @{ 'value' = "" }
        'ALZFunctionTriggerUrl' = @{ 'value' = "" }
        'BYOActionGroup' = @{ 'value' = @() }
        'BYOAlertProcessingRule' = @{ 'value' = @() }
    }
}

$paramFile = "monitoring/amba-fixed-$timestamp.json"
$params | ConvertTo-Json -Depth 10 | Set-Content $paramFile

Write-Host "Parameter file created: $paramFile" -ForegroundColor Green

# Deploy with proper location format
Write-Host "Starting AMBA deployment..." -ForegroundColor Yellow

$templateUri = "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json"

Write-Host "Template: $templateUri" -ForegroundColor Blue
Write-Host "Management Group: test-ewh" -ForegroundColor Blue
Write-Host "Location: eastus (no spaces)" -ForegroundColor Blue

# Create deployment name without spaces
$deploymentName = "AMBA-Fixed-$timestamp"
Write-Host "Deployment Name: $deploymentName" -ForegroundColor Blue

# Run deployment
Write-Host "Executing deployment..." -ForegroundColor Cyan
az deployment mg create `
    --name $deploymentName `
    --template-uri $templateUri `
    --location "eastus" `
    --management-group-id "test-ewh" `
    --parameters "@$paramFile"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ AMBA deployment successful!" -ForegroundColor Green
    
    Write-Host "`nVerifying deployment..." -ForegroundColor Cyan
    
    # Check deployment status
    Write-Host "Deployment status:" -ForegroundColor Yellow
    az deployment mg show --management-group-id "test-ewh" --name $deploymentName --query "{Status:properties.provisioningState, Timestamp:properties.timestamp}" -o table
    
    # Check policy assignments
    Write-Host "`nPolicy assignments:" -ForegroundColor Yellow
    az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh --query "[?contains(displayName, 'ALZ')].{Name:name, DisplayName:displayName}" -o table
    
    # Check resource group
    Write-Host "`nMonitoring resource group:" -ForegroundColor Yellow
    az group show --name "rg-amba-monitoring-001" --query "{Name:name, Location:location, ProvisioningState:properties.provisioningState}" -o table
    
} else {
    Write-Host "❌ AMBA deployment failed!" -ForegroundColor Red
    
    # Show deployment errors
    Write-Host "Checking deployment errors..." -ForegroundColor Yellow
    az deployment mg show --management-group-id "test-ewh" --name $deploymentName --query "properties.error" -o json
}

# Clean up
Remove-Item $paramFile -Force -ErrorAction SilentlyContinue
Write-Host "`nCleaned up parameter file" -ForegroundColor Gray

Write-Host "`nDeployment test completed!" -ForegroundColor Cyan
