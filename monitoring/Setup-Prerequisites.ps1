#Requires -<PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON>z.Profile, Az.Resources, Az.OperationalInsights, Az.Monitor

<#
.SYNOPSIS
    Setup prerequisites for Azure Monitor Baseline Alerts (AMBA) deployment

.DESCRIPTION
    This script creates the required resources for AMBA deployment:
    - Resource Group for monitoring resources
    - Log Analytics Workspace
    - Action Group for alert notifications

.PARAMETER subscriptionId
    Azure subscription ID where resources will be created

.PARAMETER resourceGroupName
    Name of the resource group to create. Defaults to EWH-Monitoring-RG

.PARAMETER location
    Azure region for resources. Defaults to East US

.PARAMETER logAnalyticsWorkspaceName
    Name of the Log Analytics workspace. Defaults to EWH-LogAnalytics

.PARAMETER actionGroupName
    Name of the Action Group. Defaults to EWH-AlertActionGroup

.PARAMETER emailAddress
    Email address for alert notifications

.EXAMPLE
    .\Setup-Prerequisites.ps1 -subscriptionId "12345678-1234-1234-1234-123456789012" -emailAddress "<EMAIL>"

.NOTES
    Author: EWH IT Team
    Version: 1.0.0
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$subscriptionId,

    [Parameter(Mandatory = $false)]
    [string]$resourceGroupName = "EWH-Monitoring-RG",

    [Parameter(Mandatory = $false)]
    [string]$location = "East US",

    [Parameter(Mandatory = $false)]
    [string]$logAnalyticsWorkspaceName = "EWH-LogAnalytics",

    [Parameter(Mandatory = $false)]
    [string]$actionGroupName = "EWH-AlertActionGroup",

    [Parameter(Mandatory = $true)]
    [string]$emailAddress
)

$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::Cyan
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

try {
    Write-ColorOutput "Setting up prerequisites for AMBA deployment..." "Cyan"
    
    # Set subscription context
    Write-ColorOutput "Setting subscription context to: $subscriptionId" "Yellow"
    Set-AzContext -SubscriptionId $subscriptionId
    
    # Create Resource Group
    Write-ColorOutput "Creating Resource Group: $resourceGroupName" "Yellow"
    $rg = Get-AzResourceGroup -Name $resourceGroupName -ErrorAction SilentlyContinue
    if (-not $rg) {
        $rg = New-AzResourceGroup -Name $resourceGroupName -Location $location
        Write-ColorOutput "Resource Group created successfully." "Green"
    } else {
        Write-ColorOutput "Resource Group already exists." "Yellow"
    }
    
    # Create Log Analytics Workspace
    Write-ColorOutput "Creating Log Analytics Workspace: $logAnalyticsWorkspaceName" "Yellow"
    $workspace = Get-AzOperationalInsightsWorkspace -ResourceGroupName $resourceGroupName -Name $logAnalyticsWorkspaceName -ErrorAction SilentlyContinue
    if (-not $workspace) {
        $workspace = New-AzOperationalInsightsWorkspace -ResourceGroupName $resourceGroupName -Name $logAnalyticsWorkspaceName -Location $location -Sku "PerGB2018"
        Write-ColorOutput "Log Analytics Workspace created successfully." "Green"
    } else {
        Write-ColorOutput "Log Analytics Workspace already exists." "Yellow"
    }
    
    # Create Action Group
    Write-ColorOutput "Creating Action Group: $actionGroupName" "Yellow"
    $actionGroup = Get-AzActionGroup -ResourceGroupName $resourceGroupName -Name $actionGroupName -ErrorAction SilentlyContinue
    if (-not $actionGroup) {
        $emailReceiver = New-AzActionGroupReceiver -Name "EWH-Admin" -EmailReceiver -EmailAddress $emailAddress
        $actionGroup = Set-AzActionGroup -ResourceGroupName $resourceGroupName -Name $actionGroupName -ShortName "EWHAlerts" -Receiver $emailReceiver
        Write-ColorOutput "Action Group created successfully." "Green"
    } else {
        Write-ColorOutput "Action Group already exists." "Yellow"
    }
    
    # Update parameters file with actual resource IDs
    Write-ColorOutput "Updating parameters file with resource IDs..." "Yellow"
    $parametersFile = "deploy-amba.parameters.json"
    $parameters = Get-Content $parametersFile | ConvertFrom-Json
    
    $parameters.parameters.logAnalyticsWorkspaceResourceId.value = $workspace.ResourceId
    $parameters.parameters.actionGroupResourceId.value = $actionGroup.Id
    
    $parameters | ConvertTo-Json -Depth 10 | Set-Content $parametersFile
    Write-ColorOutput "Parameters file updated successfully." "Green"
    
    # Display summary
    Write-ColorOutput "`nPrerequisites setup completed successfully!" "Green"
    Write-ColorOutput "Resource Group: $($rg.ResourceGroupName)" "White"
    Write-ColorOutput "Log Analytics Workspace: $($workspace.Name)" "White"
    Write-ColorOutput "Action Group: $($actionGroup.Name)" "White"
    Write-ColorOutput "`nYou can now run the AMBA deployment script." "Cyan"
    
} catch {
    Write-ColorOutput "Error setting up prerequisites: $($_.Exception.Message)" "Red"
    exit 1
}
