# Azure Monitor Baseline Alerts (AMBA) - Dual Log Analytics Workspace Architecture

## Tổng quan Kiến trúc

<PERSON>ến trúc này triển khai **2 Log Analytics Workspaces riêng biệt** trong Management/Operations Subscription để tối ưu hóa việc thu thập và phân tích dữ liệu monitoring:

### 1. **Infrastructure Log Analytics Workspace (Infrastructure LAW)**
- **M<PERSON><PERSON> đích**: <PERSON>hu thập logs và metrics từ tài nguyên hạ tầng và nền tảng
- **Retention**: 90 ngày
- **Daily Quota**: 10GB
- **Data Sources**:
  - Azure Virtual Networks (VNets)
  - Network Security Groups (NSGs) 
  - Azure Firewall
  - Application Gateways
  - Load Balancers
  - Key Vaults
  - Storage Accounts
  - Virtual Machines (Platform/Management)
  - OS logs từ Infrastructure VMs

### 2. **Security Log Analytics Workspace (Security LAW)**
- **<PERSON><PERSON><PERSON> đ<PERSON>ch**: Thu thập logs bảo mật cho Azure Sentinel và SOC
- **Retention**: 180 ngày
- **Daily Quota**: 20GB
- **Data Sources**:
  - Microsoft Entra ID Sign-in/Audit Logs
  - NSG Flow Logs
  - Azure Firewall Security Logs
  - Microsoft Defender for Cloud Alerts
  - Azure Activity Logs (Security categories)
  - Audit logs từ các services
  - Security events từ VMs

## Cấu trúc Deployment

```
monitoring/
├── deploy-amba.json                    # Main template với dual LAW
├── deploy-amba-dual-law.parameters.json # Parameters cho dual LAW
├── diagnostic-settings-policies.json   # Policies cho diagnostic settings
├── Deploy-Dual-LAW-AMBA.ps1           # Deployment script
└── README-Dual-LAW.md                 # Documentation này
```

## Deployment Process

### Prerequisites
1. Management Group đã được tạo
2. Management/Operations Subscription
3. Quyền Contributor trên Management Group và Subscription

### Deployment Steps

#### 1. Deploy Dual LAW Architecture
```powershell
.\Deploy-Dual-LAW-AMBA.ps1 -managementGroupId "test-ewh" -managementSubscriptionId "864282bd-af70-4198-9af5-2ffd74bd9b52"
```

#### 2. Hoặc sử dụng Azure CLI
```bash
# Deploy LAW và AMBA policies
az deployment mg create \
  --management-group-id "test-ewh" \
  --location "East US" \
  --template-file "deploy-amba.json" \
  --parameters "@deploy-amba-dual-law.parameters.json" \
  --name "EWH-DualLAW-AMBA"

# Deploy Diagnostic Settings policies
az deployment mg create \
  --management-group-id "test-ewh" \
  --location "East US" \
  --template-file "diagnostic-settings-policies.json" \
  --parameters managementGroupId="test-ewh" \
    infrastructureLAWResourceId="/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/EWH-Management-RG/providers/Microsoft.OperationalInsights/workspaces/EWH-Infrastructure-LAW" \
    securityLAWResourceId="/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/EWH-Management-RG/providers/Microsoft.OperationalInsights/workspaces/EWH-Security-LAW"
```

## Resources được tạo

### 1. **Management Subscription Resources**
- **Resource Group**: `EWH-Management-RG`
- **Infrastructure LAW**: `EWH-Infrastructure-LAW`
- **Security LAW**: `EWH-Security-LAW`
- **Action Group**: `EWH-AlertActionGroup`

### 2. **Management Group Level Policies**
- **AMBA Policy Definitions**: Service Health, Resource Health, Activity Log, Metric Alerts
- **Diagnostic Settings Policies**: Auto-configure log routing
- **Policy Initiative**: `EWH-MonitoringInitiative`
- **Policy Assignment**: Applied to Management Group

## Data Routing Strategy

### Infrastructure LAW receives:
- **Platform Metrics**: CPU, Memory, Disk, Network
- **Infrastructure Logs**: 
  - VNet diagnostic logs
  - Storage account logs
  - Key Vault access logs
  - VM performance counters
  - Application Gateway logs

### Security LAW receives:
- **Security Logs**:
  - Azure Activity Log (Administrative, Security, Alert, Policy)
  - NSG Flow Logs
  - Azure Firewall threat intelligence logs
  - Microsoft Defender alerts
- **Identity Logs**:
  - Entra ID Sign-in logs
  - Entra ID Audit logs
- **Compliance Logs**:
  - Policy compliance events
  - Resource changes

## Post-Deployment Configuration

### 1. Configure Azure Sentinel
```powershell
# Enable Azure Sentinel on Security LAW
$workspaceId = "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/EWH-Management-RG/providers/Microsoft.OperationalInsights/workspaces/EWH-Security-LAW"

# Install Sentinel solution
Install-AzSentinelSolution -WorkspaceId $workspaceId
```

### 2. Configure NSG Flow Logs
- Enable NSG Flow Logs cho tất cả NSGs
- Route logs đến Security LAW
- Configure Traffic Analytics

### 3. Configure Microsoft Entra ID Diagnostic Settings
- Sign-in logs → Security LAW
- Audit logs → Security LAW
- Provisioning logs → Security LAW

### 4. Assign Diagnostic Settings Policies
- Review policy compliance
- Remediate non-compliant resources
- Monitor policy effectiveness

## Monitoring và Alerting

### Infrastructure Monitoring
- **VM Performance**: CPU > 80%, Memory > 85%
- **Storage**: Availability < 99.9%
- **Network**: High latency, packet loss
- **Key Vault**: Access failures

### Security Monitoring
- **Failed logins**: Multiple failed attempts
- **Privilege escalation**: Role assignments
- **Resource changes**: Critical resource modifications
- **Network threats**: Malicious IP connections

## Cost Optimization

### Infrastructure LAW
- **Retention**: 90 ngày (balance cost vs operational needs)
- **Daily Cap**: 10GB (adjust based on environment size)
- **Data Types**: Focus on operational metrics

### Security LAW
- **Retention**: 180 ngày (compliance requirements)
- **Daily Cap**: 20GB (security data priority)
- **Data Types**: All security-relevant logs

## Troubleshooting

### Common Issues
1. **Policy Assignment Failures**: Check Management Group permissions
2. **LAW Creation Failures**: Verify subscription quotas
3. **Diagnostic Settings**: Ensure resource permissions
4. **Data Ingestion**: Check network connectivity

### Validation Commands
```bash
# Check LAW status
az monitor log-analytics workspace show --workspace-name "EWH-Infrastructure-LAW" --resource-group "EWH-Management-RG"
az monitor log-analytics workspace show --workspace-name "EWH-Security-LAW" --resource-group "EWH-Management-RG"

# Check policy compliance
az policy state list --management-group "test-ewh" --filter "policyDefinitionName eq 'EWH-DiagnosticSettings-Infrastructure'"
```

## Next Steps

1. **Configure Azure Sentinel** trên Security LAW
2. **Set up Data Connectors** cho Entra ID, Office 365, AWS
3. **Create Custom Workbooks** cho infrastructure monitoring
4. **Implement Automated Response** với Logic Apps
5. **Configure ITSM Integration** cho incident management
