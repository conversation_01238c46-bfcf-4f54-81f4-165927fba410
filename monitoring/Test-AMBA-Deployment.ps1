#Requires -Mo<PERSON>les <PERSON>.Accounts, Az.Profile, Az.Resources

<#
.SYNOPSIS
    Test Azure Monitor Baseline Alerts (AMBA) deployment for EWH Landing Zone

.DESCRIPTION
    This script validates that AMBA policies and assignments have been deployed correctly.
    It checks for policy definitions, policy set definitions, and policy assignments.

.PARAMETER enterpriseScaleCompanyPrefix
    Provide the company prefix for the management group hierarchy (max 10 characters)

.EXAMPLE
    .\Test-AMBA-Deployment.ps1 -enterpriseScaleCompanyPrefix "EWH"

.NOTES
    Author: EWH IT Team
    Version: 1.0.0
    Requires: Az PowerShell modules (Az.Accounts, Az.Profile, Az.Resources)
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$enterpriseScaleCompanyPrefix
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::Cyan
        "Magenta" = [ConsoleColor]::Magenta
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

# Function to check if user is logged in to Azure
function Test-AzureLogin {
    try {
        $context = Get-AzContext
        if ($null -eq $context) {
            return $false
        }
        return $true
    }
    catch {
        return $false
    }
}

# Main execution
try {
    Write-ColorOutput "Testing Azure Monitor Baseline Alerts (AMBA) deployment..." "Cyan"
    
    # Check if user is logged in to Azure
    if (-not (Test-AzureLogin)) {
        Write-ColorOutput "You are not logged in to Azure. Please run 'Connect-AzAccount' first." "Red"
        exit 1
    }
    
    # Get current Azure context
    $context = Get-AzContext
    Write-ColorOutput "Current Azure context: $($context.Account.Id)" "Green"
    
    $testResults = @()
    
    # Test 1: Check AMBA Policy Definitions
    Write-ColorOutput "`n=== Testing AMBA Policy Definitions ===" "Yellow"
    
    $expectedPolicies = @(
        "EWH-ServiceHealthAlerts",
        "EWH-ResourceHealthAlerts", 
        "EWH-ActivityLogAlerts",
        "EWH-VMMetricAlerts"
    )
    
    foreach ($policyName in $expectedPolicies) {
        try {
            $policy = Get-AzPolicyDefinition -ManagementGroupName $enterpriseScaleCompanyPrefix -Custom | Where-Object { $_.Name -eq $policyName }
            if ($policy) {
                Write-ColorOutput "✅ Policy Definition '$policyName' found" "Green"
                $testResults += [PSCustomObject]@{
                    Test = "Policy Definition"
                    Name = $policyName
                    Status = "PASS"
                    Details = "Found"
                }
            }
            else {
                Write-ColorOutput "❌ Policy Definition '$policyName' not found" "Red"
                $testResults += [PSCustomObject]@{
                    Test = "Policy Definition"
                    Name = $policyName
                    Status = "FAIL"
                    Details = "Not found"
                }
            }
        }
        catch {
            Write-ColorOutput "❌ Error checking Policy Definition '$policyName': $($_.Exception.Message)" "Red"
            $testResults += [PSCustomObject]@{
                Test = "Policy Definition"
                Name = $policyName
                Status = "ERROR"
                Details = $_.Exception.Message
            }
        }
    }
    
    # Test 2: Check AMBA Policy Set Definition (Initiative)
    Write-ColorOutput "`n=== Testing AMBA Policy Set Definition ===" "Yellow"
    
    try {
        $initiative = Get-AzPolicySetDefinition -ManagementGroupName $enterpriseScaleCompanyPrefix -Custom | Where-Object { $_.Name -eq "EWH-MonitoringInitiative" }
        if ($initiative) {
            Write-ColorOutput "✅ Policy Set Definition 'EWH-MonitoringInitiative' found" "Green"
            Write-ColorOutput "   Display Name: $($initiative.Properties.DisplayName)" "White"
            Write-ColorOutput "   Policy Count: $($initiative.Properties.PolicyDefinitions.Count)" "White"
            $testResults += [PSCustomObject]@{
                Test = "Policy Set Definition"
                Name = "EWH-MonitoringInitiative"
                Status = "PASS"
                Details = "Found with $($initiative.Properties.PolicyDefinitions.Count) policies"
            }
        }
        else {
            Write-ColorOutput "❌ Policy Set Definition 'EWH-MonitoringInitiative' not found" "Red"
            $testResults += [PSCustomObject]@{
                Test = "Policy Set Definition"
                Name = "EWH-MonitoringInitiative"
                Status = "FAIL"
                Details = "Not found"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error checking Policy Set Definition: $($_.Exception.Message)" "Red"
        $testResults += [PSCustomObject]@{
            Test = "Policy Set Definition"
            Name = "EWH-MonitoringInitiative"
            Status = "ERROR"
            Details = $_.Exception.Message
        }
    }
    
    # Test 3: Check AMBA Policy Assignment
    Write-ColorOutput "`n=== Testing AMBA Policy Assignment ===" "Yellow"
    
    try {
        $assignment = Get-AzPolicyAssignment -Scope "/providers/Microsoft.Management/managementGroups/$enterpriseScaleCompanyPrefix" | Where-Object { $_.Name -eq "EWH-AMBA-Assignment" }
        if ($assignment) {
            Write-ColorOutput "✅ Policy Assignment 'EWH-AMBA-Assignment' found" "Green"
            Write-ColorOutput "   Display Name: $($assignment.Properties.DisplayName)" "White"
            Write-ColorOutput "   Enforcement Mode: $($assignment.Properties.EnforcementMode)" "White"
            $testResults += [PSCustomObject]@{
                Test = "Policy Assignment"
                Name = "EWH-AMBA-Assignment"
                Status = "PASS"
                Details = "Found and assigned to management group"
            }
        }
        else {
            Write-ColorOutput "❌ Policy Assignment 'EWH-AMBA-Assignment' not found" "Red"
            $testResults += [PSCustomObject]@{
                Test = "Policy Assignment"
                Name = "EWH-AMBA-Assignment"
                Status = "FAIL"
                Details = "Not found"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error checking Policy Assignment: $($_.Exception.Message)" "Red"
        $testResults += [PSCustomObject]@{
            Test = "Policy Assignment"
            Name = "EWH-AMBA-Assignment"
            Status = "ERROR"
            Details = $_.Exception.Message
        }
    }
    
    # Test Summary
    Write-ColorOutput "`n=== Test Summary ===" "Cyan"
    
    $passCount = ($testResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failCount = ($testResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $errorCount = ($testResults | Where-Object { $_.Status -eq "ERROR" }).Count
    $totalCount = $testResults.Count
    
    Write-ColorOutput "Total Tests: $totalCount" "White"
    Write-ColorOutput "Passed: $passCount" "Green"
    Write-ColorOutput "Failed: $failCount" "Red"
    Write-ColorOutput "Errors: $errorCount" "Yellow"
    
    # Display detailed results
    Write-ColorOutput "`n=== Detailed Results ===" "Cyan"
    $testResults | Format-Table -AutoSize
    
    # Overall result
    if ($failCount -eq 0 -and $errorCount -eq 0) {
        Write-ColorOutput "`n🎉 All AMBA tests passed successfully!" "Green"
        exit 0
    }
    elseif ($failCount -gt 0) {
        Write-ColorOutput "`n❌ Some AMBA tests failed. Please check the deployment." "Red"
        exit 1
    }
    else {
        Write-ColorOutput "`n⚠️  Some AMBA tests encountered errors. Please review." "Yellow"
        exit 1
    }
}
catch {
    Write-ColorOutput "Error during AMBA testing: $($_.Exception.Message)" "Red"
    Write-ColorOutput "Stack Trace: $($_.ScriptStackTrace)" "Red"
    exit 1
}

Write-ColorOutput "AMBA testing script completed." "Cyan"
