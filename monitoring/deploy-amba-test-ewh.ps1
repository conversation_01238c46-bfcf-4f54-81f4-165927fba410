# Deploy AMBA using existing test-ewh management groups
param(
    [string]$alertEmailAddress = "<EMAIL>",
    [string]$managementSubscriptionId = "864282bd-af70-4198-9af5-2ffd74bd9b52",
    [string]$enterpriseScaleCompanyPrefix = "test-ewh"
)

$timestamp = Get-Date -Format 'yyyyMMdd-HHmmss'
$location = "East US"

Write-Host "Deploying AMBA using existing test-ewh management groups..." -ForegroundColor Cyan

# Create parameter file using existing management groups
$ambaParams = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    'contentVersion' = "*******"
    'parameters' = @{
        'enterpriseScaleCompanyPrefix' = @{ 'value' = "test-ewh" }
        'platformManagementGroup' = @{ 'value' = "test-ewh-platform" }
        'IdentityManagementGroup' = @{ 'value' = "test-ewh-identity" }
        'managementManagementGroup' = @{ 'value' = "test-ewh-management" }
        'connectivityManagementGroup' = @{ 'value' = "test-ewh-connectivity" }
        'LandingZoneManagementGroup' = @{ 'value' = "test-ewh-landingzones" }
        'enableAMBAConnectivity' = @{ 'value' = "Yes" }
        'enableAMBAIdentity' = @{ 'value' = "Yes" }
        'enableAMBAManagement' = @{ 'value' = "Yes" }
        'enableAMBAServiceHealth' = @{ 'value' = "Yes" }
        'enableAMBANotificationAssets' = @{ 'value' = "Yes" }
        'enableAMBAHybridVM' = @{ 'value' = "Yes" }
        'enableAMBAKeyManagement' = @{ 'value' = "Yes" }
        'enableAMBALoadBalancing' = @{ 'value' = "Yes" }
        'enableAMBANetworkChanges' = @{ 'value' = "Yes" }
        'enableAMBARecoveryServices' = @{ 'value' = "Yes" }
        'enableAMBAStorage' = @{ 'value' = "Yes" }
        'enableAMBAVM' = @{ 'value' = "Yes" }
        'enableAMBAWeb' = @{ 'value' = "Yes" }
        'managementSubscriptionId' = @{ 'value' = $managementSubscriptionId }
        'ALZMonitorResourceGroupName' = @{ 'value' = "rg-amba-monitoring-001" }
        'ALZMonitorResourceGroupLocation' = @{ 'value' = $location }
        'ALZMonitorActionGroupEmail' = @{ 'value' = @($alertEmailAddress) }
        'telemetryOptOut' = @{ 'value' = "No" }
        'bringYourOwnUserAssignedManagedIdentity' = @{ 'value' = "No" }
        'userAssignedManagedIdentityName' = @{ 'value' = "id-amba-test-ewh-001" }
        'ALZMonitorResourceGroupTags' = @{ 'value' = @{
            'Project' = 'amba-monitoring'
            'Environment' = 'Production'
            'CreatedBy' = 'AMBA-Official-Test'
        }}
        'ALZMonitorDisableTagName' = @{ 'value' = "MonitorDisable" }
        'ALZMonitorDisableTagValues' = @{ 'value' = @("true", "Test", "Dev", "Sandbox") }
        'ALZLogicappResourceId' = @{ 'value' = "" }
        'ALZLogicappCallbackUrl' = @{ 'value' = "" }
        'ALZArmRoleId' = @{ 'value' = @() }
        'ALZEventHubResourceId' = @{ 'value' = @() }
        'ALZWebhookServiceUri' = @{ 'value' = @() }
        'ALZFunctionResourceId' = @{ 'value' = "" }
        'ALZFunctionTriggerUrl' = @{ 'value' = "" }
        'BYOActionGroup' = @{ 'value' = @() }
        'BYOAlertProcessingRule' = @{ 'value' = @() }
    }
}

$paramFile = "monitoring/amba-test-ewh-params-$timestamp.json"
$ambaParams | ConvertTo-Json -Depth 10 | Set-Content $paramFile

Write-Host "Parameter file created: $paramFile" -ForegroundColor Green

# Show management groups being used
Write-Host "`nUsing existing management groups:" -ForegroundColor Yellow
Write-Host "Root: test-ewh" -ForegroundColor White
Write-Host "Platform: test-ewh-platform" -ForegroundColor White
Write-Host "Identity: test-ewh-identity" -ForegroundColor White
Write-Host "Management: test-ewh-management" -ForegroundColor White
Write-Host "Connectivity: test-ewh-connectivity" -ForegroundColor White
Write-Host "Landing Zones: test-ewh-landingzones" -ForegroundColor White

# Deploy AMBA
$templateUri = "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json"

Write-Host "`nStarting AMBA deployment..." -ForegroundColor Cyan
Write-Host "This will take 10-15 minutes..." -ForegroundColor Yellow

try {
    $deploymentCommand = "az deployment mg create --name `"AMBA-TestEWH-$timestamp`" --template-uri `"$templateUri`" --location `"$location`" --management-group-id `"test-ewh`" --parameters `"@$paramFile`""
    Write-Host "Command: $deploymentCommand" -ForegroundColor Gray
    
    $deploymentResult = Invoke-Expression $deploymentCommand
    
    Write-Host "✅ AMBA deployment completed!" -ForegroundColor Green
    
    Write-Host "`nWhat was deployed:" -ForegroundColor Cyan
    Write-Host "✅ Policy Definitions for AMBA alerts" -ForegroundColor Green
    Write-Host "✅ Policy Set Definitions (Initiatives)" -ForegroundColor Green
    Write-Host "✅ Policy Assignments to management groups:" -ForegroundColor Green
    Write-Host "   - Connectivity Initiative → test-ewh-connectivity" -ForegroundColor White
    Write-Host "   - Identity Initiative → test-ewh-identity" -ForegroundColor White
    Write-Host "   - Management Initiative → test-ewh-management" -ForegroundColor White
    Write-Host "   - Landing Zone Initiative → test-ewh-landingzones" -ForegroundColor White
    Write-Host "   - Service Health Initiative → test-ewh" -ForegroundColor White
    Write-Host "✅ Monitoring Resource Group: rg-amba-monitoring-001" -ForegroundColor Green
    Write-Host "✅ Action Group for email notifications" -ForegroundColor Green
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Check Azure Portal → Management Groups → Policies" -ForegroundColor White
    Write-Host "2. Verify policy assignments are present" -ForegroundColor White
    Write-Host "3. Run remediation tasks for existing resources" -ForegroundColor White
    Write-Host "4. Deploy test resources to verify automatic alert creation" -ForegroundColor White
    
    Write-Host "`nVerification Commands:" -ForegroundColor Blue
    Write-Host "# Check policy assignments" -ForegroundColor White
    Write-Host "az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh" -ForegroundColor Gray
    Write-Host "`n# Check policy compliance" -ForegroundColor White
    Write-Host "az policy state list --management-group test-ewh" -ForegroundColor Gray
    
}
catch {
    Write-Host "❌ Deployment failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Clean up
Remove-Item $paramFile -Force -ErrorAction SilentlyContinue
Write-Host "`nCleaned up parameter file" -ForegroundColor Yellow

Write-Host "`nAMBA deployment script completed!" -ForegroundColor Cyan
