# Simple AMBA test deployment
$timestamp = Get-Date -Format 'yyyyMMdd-HHmmss'

Write-Host "Creating AMBA parameter file..." -ForegroundColor Cyan

# Create minimal parameter file
$params = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    'contentVersion' = "*******"
    'parameters' = @{
        'enterpriseScaleCompanyPrefix' = @{ 'value' = "test-ewh" }
        'platformManagementGroup' = @{ 'value' = "test-ewh-platform" }
        'IdentityManagementGroup' = @{ 'value' = "test-ewh-identity" }
        'managementManagementGroup' = @{ 'value' = "test-ewh-management" }
        'connectivityManagementGroup' = @{ 'value' = "test-ewh-connectivity" }
        'LandingZoneManagementGroup' = @{ 'value' = "test-ewh-landingzones" }
        'enableAMBAConnectivity' = @{ 'value' = "Yes" }
        'enableAMBAIdentity' = @{ 'value' = "Yes" }
        'enableAMBAManagement' = @{ 'value' = "Yes" }
        'enableAMBAServiceHealth' = @{ 'value' = "Yes" }
        'enableAMBANotificationAssets' = @{ 'value' = "Yes" }
        'managementSubscriptionId' = @{ 'value' = "864282bd-af70-4198-9af5-2ffd74bd9b52" }
        'ALZMonitorResourceGroupName' = @{ 'value' = "rg-amba-monitoring-001" }
        'ALZMonitorResourceGroupLocation' = @{ 'value' = "East US" }
        'ALZMonitorActionGroupEmail' = @{ 'value' = @("<EMAIL>") }
        'telemetryOptOut' = @{ 'value' = "No" }
        'bringYourOwnUserAssignedManagedIdentity' = @{ 'value' = "No" }
        'userAssignedManagedIdentityName' = @{ 'value' = "id-amba-test-ewh-001" }
        'ALZEventHubResourceId' = @{ 'value' = @() }
        'ALZWebhookServiceUri' = @{ 'value' = @() }
        'ALZArmRoleId' = @{ 'value' = @() }
        'BYOActionGroup' = @{ 'value' = @() }
        'BYOAlertProcessingRule' = @{ 'value' = @() }
    }
}

$paramFile = "monitoring/amba-simple-$timestamp.json"
$params | ConvertTo-Json -Depth 10 | Set-Content $paramFile

Write-Host "Parameter file created: $paramFile" -ForegroundColor Green

# Deploy
Write-Host "Starting AMBA deployment..." -ForegroundColor Yellow

$templateUri = "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json"

Write-Host "Template: $templateUri" -ForegroundColor Blue
Write-Host "Management Group: test-ewh" -ForegroundColor Blue

# Run deployment
az deployment mg create `
    --name "AMBA-Simple-$timestamp" `
    --template-uri $templateUri `
    --location "East US" `
    --management-group-id "test-ewh" `
    --parameters "@$paramFile"

if ($LASTEXITCODE -eq 0) {
    Write-Host "AMBA deployment successful!" -ForegroundColor Green
    
    Write-Host "`nVerifying deployment..." -ForegroundColor Cyan
    
    # Check policy assignments
    Write-Host "Policy assignments:" -ForegroundColor Yellow
    az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh --query "[?contains(displayName, 'ALZ')].{Name:name, DisplayName:displayName}" -o table
    
    # Check resource group
    Write-Host "`nMonitoring resource group:" -ForegroundColor Yellow
    az group show --name "rg-amba-monitoring-001" --query "{Name:name, Location:location, ProvisioningState:properties.provisioningState}" -o table
    
} else {
    Write-Host "AMBA deployment failed!" -ForegroundColor Red
}

# Clean up
Remove-Item $paramFile -Force -ErrorAction SilentlyContinue
Write-Host "Cleaned up parameter file" -ForegroundColor Gray
