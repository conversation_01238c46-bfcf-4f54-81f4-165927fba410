# Azure Landing Zone Monitoring Templates

## 🎯 **IMPORTANT: AMBA vs Custom Templates**

### **Current Templates (Custom Infrastructure)**
- ✅ `deploy-law.json` - Creates Log Analytics Workspaces
- ✅ `deploy-amba.json` - Creates Action Groups and basic monitoring resources
- ❌ **Does NOT create AMBA policies** (automatic alert creation)

### **Official AMBA (Policy-Driven Monitoring)**
- ✅ `deploy-amba-official.json` - Deploys Microsoft's official AMBA
- ✅ `Deploy-AMBA-Direct.ps1` - Script for official AMBA deployment
- ✅ **Creates policy definitions and assignments** for automatic alerts

## Architecture

### 1. Custom Infrastructure Templates

#### Dual LAW Design
- **Infrastructure LAW**: General monitoring, metrics, performance data (90-day retention)
- **Security LAW**: Security events, audit logs, compliance data (180-day retention)

#### Resource Groups
- **EWH-mgmt-rg**: Management resources (Infrastructure LAW)
- **EWH-sec-rg**: Security resources (Security LAW)
- **rg-amba-monitoring-001**: AMBA monitoring resources (Action Groups, Alerts)

### 2. Official AMBA Solution

#### What Official AMBA Provides:
- 🔧 **Policy Definitions**: Rules for creating alerts automatically
- 📋 **Policy Initiatives**: Grouped policies for different workloads
- 🎯 **Policy Assignments**: Assigns policies to management groups
- ⚙️ **Deploy If Not Exists (DINE)**: Automatically creates alerts for new resources
- 📧 **Action Groups**: Email notifications for alerts

#### Management Group Assignments:
- **Connectivity Initiative** → `{prefix}-connectivity`
- **Identity Initiative** → `{prefix}-identity`
- **Management Initiative** → `{prefix}-management`
- **Landing Zone Initiative** → `{prefix}-landingzones`
- **Service Health Initiative** → `{prefix}` (root)

## Templates

| Template | Purpose | Type | Resources Created |
|----------|---------|------|-------------------|
| `deploy-law.json` | Log Analytics Workspaces | Infrastructure | Management RG, Security RG, LAWs |
| `deploy-amba.json` | AMBA Resources | Infrastructure | Monitoring RG, Action Group |
| `deploy-amba-official.json` | Official AMBA | Policy-Driven | Policy Definitions, Assignments, Initiatives |

## Deployment Options

### Option 1: Custom Infrastructure Only
```powershell
# Deploy foundation infrastructure
.\Deploy-ALZ-Simple.ps1 -alertEmailAddress "<EMAIL>"
```

### Option 2: Official AMBA (Recommended)
```powershell
# Deploy official AMBA with policies
.\Deploy-AMBA-Direct.ps1 -alertEmailAddress "<EMAIL>" -managementSubscriptionId "your-subscription-id"
```

### Option 3: Manual Official AMBA
```bash
# Deploy official AMBA directly from Microsoft repository
az deployment mg create \
  --management-group-id "your-mg" \
  --location "East US" \
  --template-uri "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json" \
  --parameters "@amba-params.json" \
  --name "AMBA-Official"
```

## Key Differences

| Feature | Custom Templates | Official AMBA |
|---------|------------------|---------------|
| **Infrastructure** | ✅ Creates LAW, RG, Action Groups | ✅ Creates LAW, RG, Action Groups |
| **Policy Definitions** | ❌ No | ✅ 100+ alert policies |
| **Automatic Alerts** | ❌ Manual setup required | ✅ Automatic via DINE policies |
| **Management Group Scope** | ❌ Subscription only | ✅ Management Group hierarchy |
| **Compliance** | ❌ Manual monitoring | ✅ Policy compliance tracking |
| **Remediation** | ❌ Manual | ✅ Automated remediation tasks |

## Configuration

### Official AMBA Parameters
```json
{
  "enterpriseScaleCompanyPrefix": "test-ewh",
  "alertEmailAddress": "<EMAIL>",
  "managementSubscriptionId": "864282bd-af70-4198-9af5-2ffd74bd9b52",
  "enableAllInitiatives": true,
  "ambaVersion": "2025-07-02"
}
```

### Custom Templates Configuration
- **Infrastructure LAW**: 90-day retention, 10GB daily quota
- **Security LAW**: 180-day retention, 20GB daily quota
- **Action Groups**: Email notifications to specified address

## Verification

### Check Official AMBA Deployment
```bash
# Check policy assignments
az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh

# Check policy compliance
az policy state list --management-group test-ewh

# Check monitoring resource group
az group show --name rg-amba-monitoring-001
```

## Troubleshooting

### Common Issues

1. **Management Group Not Found**
   - Ensure management group hierarchy exists
   - Use correct management group names in parameters

2. **Policy Assignment Failures**
   - Check permissions on management groups
   - Verify subscription is in correct management group

3. **No Automatic Alerts**
   - Custom templates don't create policies
   - Use official AMBA for automatic alert creation

## Benefits

### Custom Templates
1. **Separation of Concerns**: Infrastructure vs Security monitoring
2. **Cost Optimization**: Different retention policies
3. **Compliance**: Security data retained longer
4. **ALZ Compliant**: Follows Azure Landing Zone patterns

### Official AMBA
1. **Policy-Driven**: Uses Azure Policy for governance
2. **Automatic**: Alerts created automatically for new resources
3. **Compliant**: Follows Microsoft's monitoring best practices
4. **Scalable**: Works across entire management group hierarchy
5. **Maintainable**: Microsoft maintains and updates policies

## Next Steps After AMBA Deployment

1. **Verify Policy Assignments**: Check Azure Portal → Management Groups → Policies
2. **Run Remediation Tasks**: For existing resources not compliant
3. **Test Alert Creation**: Deploy test resources to verify automatic alerts
4. **Configure Notifications**: Verify email notifications work
5. **Monitor Compliance**: Track policy compliance over time

## Summary

✅ **Templates đã được tách thành công:**
- **1 file cho LAW** (`deploy-law.json`)
- **1 file cho AMBA** (`deploy-amba.json`) 
- **1 file cho Official AMBA** (`deploy-amba-official.json`)

✅ **Để có AMBA policies thực sự (tự động tạo alerts):**
- Sử dụng `Deploy-AMBA-Direct.ps1` hoặc `deploy-amba-official.json`
- Deploy vào management group hierarchy
- Policies sẽ tự động tạo alerts cho resources mới
