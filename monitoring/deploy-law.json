{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Deploy Log Analytics Workspaces for Azure Landing Zone - Management and Security"}, "parameters": {"companyPrefix": {"type": "string", "defaultValue": "EWH", "maxLength": 10, "metadata": {"description": "Company prefix for resource naming"}}, "location": {"type": "string", "defaultValue": "East US", "metadata": {"description": "Azure region for all resources"}}, "infrastructureLAWRetentionDays": {"type": "int", "defaultValue": 90, "metadata": {"description": "Retention days for Infrastructure Log Analytics Workspace"}}, "securityLAWRetentionDays": {"type": "int", "defaultValue": 180, "metadata": {"description": "Retention days for Security Log Analytics Workspace"}}, "infrastructureLAWDailyQuotaGb": {"type": "int", "defaultValue": 10, "metadata": {"description": "Daily quota in GB for Infrastructure LAW"}}, "securityLAWDailyQuotaGb": {"type": "int", "defaultValue": 20, "metadata": {"description": "Daily quota in GB for Security LAW"}}}, "variables": {"managementResourceGroupName": "[concat(parameters('companyPrefix'), '-mgmt-rg')]", "securityResourceGroupName": "[concat(parameters('companyPrefix'), '-sec-rg')]", "infrastructureLAWName": "[concat(parameters('companyPrefix'), '-mgmt-law')]", "securityLAWName": "[concat(parameters('companyPrefix'), '-sec-law')]", "infrastructureLAWResourceId": "[concat('/subscriptions/', subscription().subscriptionId, '/resourceGroups/', variables('managementResourceGroupName'), '/providers/Microsoft.OperationalInsights/workspaces/', variables('infrastructureLAWName'))]", "securityLAWResourceId": "[concat('/subscriptions/', subscription().subscriptionId, '/resourceGroups/', variables('securityResourceGroupName'), '/providers/Microsoft.OperationalInsights/workspaces/', variables('securityLAWName'))]"}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2021-04-01", "name": "[variables('managementResourceGroupName')]", "location": "[parameters('location')]", "tags": {"Purpose": "Management and Operations", "Environment": "Production", "CreatedBy": "ARM-LAW-Template", "ALZ-Function": "Management"}}, {"type": "Microsoft.Resources/resourceGroups", "apiVersion": "2021-04-01", "name": "[variables('securityResourceGroupName')]", "location": "[parameters('location')]", "tags": {"Purpose": "Security and Sentinel", "Environment": "Production", "CreatedBy": "ARM-LAW-Template", "ALZ-Function": "Security"}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "InfrastructureLAW-Deployment", "resourceGroup": "[variables('managementResourceGroupName')]", "dependsOn": ["[resourceId('Microsoft.Resources/resourceGroups', variables('managementResourceGroupName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "resources": [{"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2021-12-01-preview", "name": "[variables('infrastructureLAWName')]", "location": "[parameters('location')]", "properties": {"sku": {"name": "PerGB2018"}, "retentionInDays": "[parameters('infrastructureLAWRetentionDays')]", "features": {"enableLogAccessUsingOnlyResourcePermissions": true}, "workspaceCapping": {"dailyQuotaGb": "[parameters('infrastructureLAWDailyQuotaGb')]"}}, "tags": {"Purpose": "Infrastructure Monitoring", "DataClassification": "Infrastructure", "Environment": "Production"}}]}}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2021-04-01", "name": "SecurityLAW-Deployment", "resourceGroup": "[variables('securityResourceGroupName')]", "dependsOn": ["[resourceId('Microsoft.Resources/resourceGroups', variables('securityResourceGroupName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "resources": [{"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2021-12-01-preview", "name": "[variables('securityLAWName')]", "location": "[parameters('location')]", "properties": {"sku": {"name": "PerGB2018"}, "retentionInDays": "[parameters('securityLAWRetentionDays')]", "features": {"enableLogAccessUsingOnlyResourcePermissions": true}, "workspaceCapping": {"dailyQuotaGb": "[parameters('securityLAWDailyQuotaGb')]"}}, "tags": {"Purpose": "Security Monitoring and Azure Sentinel", "DataClassification": "Security", "Environment": "Production"}}]}}}], "outputs": {"managementResourceGroupName": {"type": "string", "value": "[variables('managementResourceGroupName')]"}, "securityResourceGroupName": {"type": "string", "value": "[variables('securityResourceGroupName')]"}, "infrastructureLAWResourceId": {"type": "string", "value": "[variables('infrastructureLAWResourceId')]"}, "securityLAWResourceId": {"type": "string", "value": "[variables('securityLAWResourceId')]"}, "infrastructureLAWName": {"type": "string", "value": "[variables('infrastructureLAWName')]"}, "securityLAWName": {"type": "string", "value": "[variables('securityLAWName')]"}}}