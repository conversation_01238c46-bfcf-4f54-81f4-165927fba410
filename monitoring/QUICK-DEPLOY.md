# Quick AMBA Deployment Guide

## ✅ **Đ<PERSON> sửa tất cả lỗi!**

### 🔧 **Các lỗi đã được sửa:**
- ✅ Parameter types: `ALZEventHubResourceId`, `BYOActionGroup`, `BYOAlertProcessingRule` → Array
- ✅ Location format: `"East US"` → `"eastus"` (no spaces)
- ✅ Deployment name: Removed spaces to avoid invalid characters

## 🚀 **Quick Deployment Commands**

### Step 1: Login và set subscription
```bash
az login
az account set --subscription "864282bd-af70-4198-9af5-2ffd74bd9b52"
```

### Step 2: Deploy AMBA (One command)
```bash
az deployment mg create \
  --name "AMBA-Quick-$(date +%Y%m%d-%H%M%S)" \
  --template-uri "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json" \
  --location "eastus" \
  --management-group-id "test-ewh" \
  --parameters "@monitoring/deploy-amba-manual.json"
```

### Step 3: Verify deployment
```bash
# Check deployment status
az deployment mg list --management-group-id "test-ewh" --query "[?contains(name, 'AMBA')].{Name:name, Status:properties.provisioningState, Timestamp:properties.timestamp}" -o table

# Check policy assignments
az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh --query "[?contains(displayName, 'ALZ')].{Name:name, DisplayName:displayName}" -o table

# Check resource group
az group show --name "rg-amba-monitoring-001"
```

## 📋 **Parameter File Ready**

File `monitoring/deploy-amba-manual.json` đã được chuẩn bị với:
- ✅ Tất cả parameters đúng type
- ✅ Location format đúng: `"eastus"`
- ✅ Management groups: `test-ewh-*`
- ✅ Email: `<EMAIL>`
- ✅ Subscription: `864282bd-af70-4198-9af5-2ffd74bd9b52`

## 🎯 **Expected Results**

Sau khi deploy thành công:

### Policy Assignments sẽ được tạo:
- **ALZ-Connectivity** → `test-ewh-connectivity`
- **ALZ-Identity** → `test-ewh-identity`  
- **ALZ-Management** → `test-ewh-management`
- **ALZ-LandingZone** → `test-ewh-landingzones`
- **ALZ-ServiceHealth** → `test-ewh`

### Resources sẽ được tạo:
- **Resource Group**: `rg-amba-monitoring-001`
- **Action Group**: Email notifications
- **Managed Identity**: `id-amba-test-ewh-001`

### Automatic Monitoring:
- ✅ VM alerts tự động
- ✅ Storage alerts tự động  
- ✅ Network alerts tự động
- ✅ Service Health alerts
- ✅ Email notifications

## 🔧 **Troubleshooting**

### Nếu gặp lỗi "No subscription found":
```bash
az login
az account list --output table
az account set --subscription "864282bd-af70-4198-9af5-2ffd74bd9b52"
```

### Nếu gặp lỗi "Management group not found":
```bash
az account management-group list --query "[].{Name:name, DisplayName:displayName}" -o table
```

### Nếu gặp lỗi permission:
- Cần role **Owner** hoặc **Contributor** trên management group
- Cần role **Policy Contributor** để tạo policy assignments

## ✅ **Success Verification**

Deployment thành công khi:
```bash
# 1. Deployment status = Succeeded
az deployment mg show --management-group-id "test-ewh" --name "AMBA-Quick-YYYYMMDD-HHMMSS" --query "properties.provisioningState"

# 2. Policy assignments exist
az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh --query "length([?contains(displayName, 'ALZ')])"

# 3. Resource group created
az group exists --name "rg-amba-monitoring-001"
```

## 🎉 **Next Steps**

1. **Test automatic alerts**: Deploy một VM để test
2. **Check email notifications**: Verify alerts đến email
3. **Run remediation**: Cho existing resources
4. **Monitor compliance**: Track policy compliance

**Bây giờ bạn có thể deploy AMBA chính thức với một command!** 🚀
