# Simple AMBA Deployment Commands

## 🔧 **Fix Deployment Conflict**

Lỗi `InvalidDeploymentLocation` x<PERSON>y ra vì đã có deployment tên `AMBA-OFFICIAL-DEPLOYMENT` tồn tại.

### Option 1: Xóa deployment cũ và deploy lại
```bash
# 1. Login
az login
az account set --subscription "864282bd-af70-4198-9af5-2ffd74bd9b52"

# 2. Xóa deployments cũ
az deployment mg delete --management-group-id "test-ewh" --name "AMBA-OFFICIAL-DEPLOYMENT"

# 3. Deploy với tên mới
az deployment mg create \
  --name "AMBA-New-$(date +%Y%m%d-%H%M%S)" \
  --template-uri "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json" \
  --location "eastus" \
  --management-group-id "test-ewh" \
  --parameters "@monitoring/deploy-amba-manual.json"
```

### Option 2: Sử dụng script cleanup tự động
```bash
powershell -ExecutionPolicy Bypass -File "monitoring/cleanup-and-deploy.ps1"
```

### Option 3: Deploy với tên khác (nhanh nhất)
```bash
az deployment mg create \
  --name "AMBA-Fixed-$(date +%Y%m%d-%H%M%S)" \
  --template-uri "https://raw.githubusercontent.com/Azure/azure-monitor-baseline-alerts/2025-07-02/patterns/alz/alzArm.json" \
  --location "eastus" \
  --management-group-id "test-ewh" \
  --parameters "@monitoring/deploy-amba-manual.json"
```

## ✅ **Verification Commands**

Sau khi deploy thành công:

```bash
# Check deployment status
az deployment mg list --management-group-id "test-ewh" --query "[?contains(name, 'AMBA')].{Name:name, Status:properties.provisioningState, Timestamp:properties.timestamp}" -o table

# Check policy assignments
az policy assignment list --scope /providers/Microsoft.Management/managementGroups/test-ewh --query "[?contains(displayName, 'ALZ')].{Name:name, DisplayName:displayName}" -o table

# Check resource group
az group show --name "rg-amba-monitoring-001" --query "{Name:name, Location:location, ProvisioningState:properties.provisioningState}" -o table

# Check action group
az monitor action-group list --resource-group "rg-amba-monitoring-001" -o table
```

## 🎯 **Expected Results**

Deployment thành công sẽ tạo:

### Policy Assignments:
- **ALZ-Connectivity** → `test-ewh-connectivity`
- **ALZ-Identity** → `test-ewh-identity`
- **ALZ-Management** → `test-ewh-management`
- **ALZ-LandingZone** → `test-ewh-landingzones`
- **ALZ-ServiceHealth** → `test-ewh`

### Resources:
- **Resource Group**: `rg-amba-monitoring-001`
- **Action Group**: Email notifications
- **Managed Identity**: `id-amba-test-ewh-001`

### Automatic Monitoring:
- ✅ VM alerts tự động khi tạo VM mới
- ✅ Storage alerts tự động khi tạo storage account
- ✅ Network alerts tự động khi tạo network resources
- ✅ Service Health alerts cho subscription
- ✅ Email notifications tới `<EMAIL>`

## 🚀 **Quick Test**

Để test automatic alerts, tạo một VM:

```bash
# Create test resource group
az group create --name "test-vm-rg" --location "eastus"

# Create test VM (alerts sẽ được tạo tự động)
az vm create \
  --resource-group "test-vm-rg" \
  --name "test-vm" \
  --image "Ubuntu2204" \
  --admin-username "azureuser" \
  --generate-ssh-keys

# Check if alerts were created automatically
az monitor metrics alert list --resource-group "test-vm-rg" -o table
```

## 🎉 **Success!**

Khi thấy output như này là thành công:
- ✅ Deployment status: `Succeeded`
- ✅ Policy assignments: Có các ALZ policies
- ✅ Resource group: `rg-amba-monitoring-001` exists
- ✅ Action group: Configured với email

**AMBA chính thức đã được deploy thành công!** 🚀
