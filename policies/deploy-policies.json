{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "EWH Landing Zone Policies Module", "version": "1.0.0"}, "description": "This template creates and assigns Azure policies and initiatives to EWH Landing Zone management groups"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "allowedLocations": {"type": "array", "defaultValue": ["East Asia", "Southeast Asia"], "metadata": {"description": "List of allowed Azure regions"}}, "tagPolicyParameters": {"type": "object", "defaultValue": {"requiredTags": ["Owner", "Organization", "CreatedBy", "OperationTeam", "ProjectName", "Environment", "ApplicationName", "ResourceType", "Priority", "DataZone", "CostCenter", "ManagedBy"]}, "metadata": {"description": "Parameters for tagging policy with comprehensive tag requirements"}}}, "variables": {"mgmtGroups": {"eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "LandingZone", "ldzPrd": "ldz-prd", "ldzPrdLegacy": "ldz-prd-legacy", "ldzPrdMicrosvc": "ldz-prd-microsvc", "ldzNonPrd": "ldz-non-prd", "ldzNonPrdUat": "ldz-non-prd-uat", "ldzNonPrdDev": "ldz-non-prd-dev", "sandboxes": "Sandbox", "decommissioned": "Decommissioned"}, "policyDefinitionIds": {"allowedLocations": "/providers/Microsoft.Authorization/policyDefinitions/e56962a6-4747-49cd-b67b-bf8b01975c4c", "secureTransferStorage": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "keyVaultDeletionProtection": "/providers/Microsoft.Authorization/policyDefinitions/0b60c0b2-2dc2-4e1c-b5c9-abbed971de53", "storagePublicAccess": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751", "blockSshRdpBuiltIn": "/providers/Microsoft.Authorization/policyDefinitions/2c89a2e5-7285-40fe-afe0-ae8654b92fab", "nsgAssociation": "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517"}}, "resources": [{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "EWH-NamingConvention", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention", "policyType": "Custom", "mode": "All", "metadata": {"category": "Naming"}, "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'rg'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'vnet'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Compute/virtualMachines"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'vm'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"anyOf": [{"value": "[[not(contains(toLower(field('name')), 'stg'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Web/sites"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'app'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.KeyVault/vaults"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'kv'))]", "equals": true}]}]}]}, "then": {"effect": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "EWH-NamingConvention-Databases", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Databases", "description": "This policy enforces the EWH Landing Zone naming convention for database resources", "mode": "All", "policyType": "Custom", "metadata": {"category": "Naming", "version": "1.0.0"}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'sql'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers/databases"}, {"anyOf": [{"value": "[[not(equals(toLower(split(split(field('name'), '/')[1], '-')[3]), 'sql'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/servers"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'mysql'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/flexibleServers"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'mysql'))]", "equals": true}]}]}]}, "then": {"effect": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "EWH-NamingConvention-Containers", "properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Containers", "description": "This policy enforces the EWH Landing Zone naming convention for container resources", "mode": "All", "policyType": "Custom", "metadata": {"category": "Naming", "version": "1.0.0"}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.ContainerService/managedClusters"}, {"anyOf": [{"value": "[[not(equals(toLower(split(field('name'), '-')[3]), 'aks'))]", "equals": true}]}]}]}, "then": {"effect": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "EWH-TaggingPolicy", "properties": {"displayName": "Require mandatory tags on resources with specific rules", "description": "This policy enforces required tags on Azure resources with specific validation rules", "mode": "Indexed", "policyType": "Custom", "metadata": {"category": "Tags", "version": "2.0.0"}, "parameters": {"owner": {"type": "String", "metadata": {"displayName": "Owner <PERSON>", "description": "Email of responsible owner - Required for Resource Groups and critical resources (VM, Database, App Service)"}, "defaultValue": "owner"}, "org": {"type": "String", "metadata": {"displayName": "Organization Tag", "description": "Name of the organization - Required for all resources in Subscription"}, "defaultValue": "org"}, "created_by": {"type": "String", "metadata": {"displayName": "Created By Tag", "description": "Email of creator"}, "defaultValue": "created_by"}, "operation_team": {"type": "String", "metadata": {"displayName": "Operation Team Tag", "description": "Name of operational team"}, "defaultValue": "operation_team"}, "project_name": {"type": "String", "metadata": {"displayName": "Project Name Tag", "description": "Name of the project"}, "defaultValue": "project_name"}, "env": {"type": "String", "metadata": {"displayName": "Environment Tag", "description": "Environment name - Required for all resources in Subscription"}, "allowedValues": ["Production", "Development", "Testing", "UAT"], "defaultValue": "env"}, "app_name": {"type": "String", "metadata": {"displayName": "App Name Tag", "description": "Name of the application"}, "defaultValue": "app_name"}, "resource_type": {"type": "String", "metadata": {"displayName": "Resource Type Tag", "description": "Resource classification (Infrastructure, Application, Data, Security)"}, "allowedValues": ["Infrastructure", "Application", "Data", "Security"], "defaultValue": "resource_type"}, "priority": {"type": "String", "metadata": {"displayName": "Priority Tag", "description": "Priority level - Required for critical resources (VM, Database, App Service)"}, "allowedValues": ["High", "Medium", "Low"], "defaultValue": "priority"}, "data_zone": {"type": "String", "metadata": {"displayName": "Data Zone Tag", "description": "Data zone (Public, Internal, Confidential, Restricted)"}, "allowedValues": ["Public", "Internal", "Confidential", "Restricted"], "defaultValue": "data_zone"}, "cost_center": {"type": "String", "metadata": {"displayName": "Cost Center Tag", "description": "Cost center code for financial tracking"}, "defaultValue": "cost_center"}, "managed_by": {"type": "String", "metadata": {"displayName": "Managed By Tag", "description": "Tool or team managing the resource"}, "defaultValue": "managed_by"}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "[concat('tags[', parameters('org'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('env'), ']')]", "exists": false}]}, {"allOf": [{"anyOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"field": "type", "like": "Microsoft.Compute/virtualMachines*"}, {"field": "type", "like": "Microsoft.Sql/*"}, {"field": "type", "like": "Microsoft.DBforMySQL/*"}, {"field": "type", "like": "Microsoft.DBforPostgreSQL/*"}, {"field": "type", "like": "Microsoft.DocumentDB/*"}, {"field": "type", "like": "Microsoft.Web/sites*"}, {"field": "type", "like": "Microsoft.Web/serverfarms*"}]}, {"field": "[concat('tags[', parameters('owner'), ']')]", "exists": false}]}, {"allOf": [{"anyOf": [{"field": "type", "like": "Microsoft.Compute/virtualMachines*"}, {"field": "type", "like": "Microsoft.Sql/*"}, {"field": "type", "like": "Microsoft.DBforMySQL/*"}, {"field": "type", "like": "Microsoft.DBforPostgreSQL/*"}, {"field": "type", "like": "Microsoft.DocumentDB/*"}, {"field": "type", "like": "Microsoft.Web/sites*"}, {"field": "type", "like": "Microsoft.Web/serverfarms*"}]}, {"field": "[concat('tags[', parameters('priority'), ']')]", "exists": false}]}]}, "then": {"effect": "deny"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "EWH-BlockSshRdp", "properties": {"displayName": "Block SSH/RDP access from Internet to Virtual Machines", "description": "This policy prevents direct SSH/RDP access from the Internet to virtual machines by denying: 1) NSG rules that allow SSH/RDP from Internet, and 2) Network interfaces with public IP addresses. This enforces secure access through Azure Bastion, VPN, or private connectivity.", "policyType": "Custom", "mode": "Indexed", "metadata": {"category": "Compute", "version": "1.0.0"}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups/securityRules"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "in": ["22", "3389"]}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "equals": "*"}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "0.0.0.0/0"}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"count": {"field": "Microsoft.Network/networkInterfaces/ipConfigurations[*]", "where": {"field": "Microsoft.Network/networkInterfaces/ipConfigurations[*].publicIPAddress.id", "exists": "true"}}, "greater": 0}]}]}, "then": {"effect": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2021-06-01", "name": "EWH-PublicIPNotAllowed", "properties": {"displayName": "Block SSH/RDP access from Internet to Virtual Machines", "description": "This policy prevents direct SSH/RDP access from the Internet to virtual machines by denying: 1) NSG rules that allow SSH/RDP from Internet, and 2) Network interfaces with public IP addresses. This enforces secure access through Azure Bastion, VPN, or private connectivity.", "policyType": "Custom", "mode": "Indexed", "metadata": {"category": "Compute", "version": "1.0.0"}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups/securityRules"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "in": ["22", "3389"]}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "equals": "*"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRanges[*]", "where": {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRanges[*]", "in": ["22", "3389"]}}, "greater": 0}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "0.0.0.0/0"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "where": {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefixes[*]", "equals": "0.0.0.0/0"}]}}, "greater": 0}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*]", "where": {"allOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRange", "in": ["22", "3389"]}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRange", "equals": "*"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRanges[*]", "where": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].destinationPortRanges[*]", "in": ["22", "3389"]}}, "greater": 0}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefix", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefix", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefix", "equals": "0.0.0.0/0"}, {"count": {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "where": {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules[*].sourceAddressPrefixes[*]", "equals": "0.0.0.0/0"}]}}, "greater": 0}]}]}}, "greater": 0}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"count": {"field": "Microsoft.Network/networkInterfaces/ipConfigurations[*]", "where": {"field": "Microsoft.Network/networkInterfaces/ipConfigurations[*].publicIPAddress.id", "exists": "true"}}, "greater": 0}]}]}, "then": {"effect": "<PERSON><PERSON>"}}}}, {"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2021-06-01", "name": "EWH-NamingConventionInitiative", "dependsOn": ["[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-NamingConvention')]", "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-NamingConvention-Databases')]", "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-NamingConvention-Containers')]"], "properties": {"displayName": "EWH Landing Zone Naming Convention Initiative", "description": "This initiative includes all EWH Landing Zone naming convention policies", "metadata": {"category": "Naming", "version": "1.0.0"}, "policyDefinitions": [{"policyDefinitionId": "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-NamingConvention')]"}, {"policyDefinitionId": "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-NamingConvention-Databases')]"}, {"policyDefinitionId": "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-NamingConvention-Containers')]"}]}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "EWH-NamingInit-Assign", "dependsOn": ["[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policySetDefinitions', 'EWH-NamingConventionInitiative')]"], "properties": {"displayName": "EWH Landing Zone Naming Convention Initiative Assignment", "description": "Assignment of EWH Landing Zone Naming Convention Initiative to EWH management group", "policyDefinitionId": "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policySetDefinitions', 'EWH-NamingConventionInitiative')]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "EWH-Tagging-Assign", "dependsOn": ["[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-TaggingPolicy')]"], "properties": {"displayName": "EWH Tagging Policy Assignment", "description": "Assignment of EWH Tagging Policy to EWH management group", "policyDefinitionId": "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-TaggingPolicy')]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "EWH-BlockSshRdp-Assign", "dependsOn": ["[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-BlockSshRdp')]"], "properties": {"displayName": "Block SSH/RDP from Internet Assignment", "description": "Assignment of Block SSH/RDP from Internet policy to EWH management group", "policyDefinitionId": "[extensionResourceId(managementGroup().id, 'Microsoft.Authorization/policyDefinitions', 'EWH-BlockSshRdp')]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "AllowedLoc-Assign", "properties": {"displayName": "Allowed Locations Assignment", "description": "Assignment of Allowed Locations built-in policy to EWH management group", "policyDefinitionId": "[variables('policyDefinitionIds').allowedLocations]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>", "parameters": {"listOfAllowedLocations": {"value": "[parameters('allowedLocations')]"}}}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "SecureTransfer-Assign", "properties": {"displayName": "Secure Transfer to Storage Accounts Assignment", "description": "Assignment of Secure Transfer to Storage Accounts built-in policy to EWH management group", "policyDefinitionId": "[variables('policyDefinitionIds').secureTransferStorage]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Assign", "properties": {"displayName": "Key Vault Deletion Protection Assignment", "description": "Assignment of Key Vault Deletion Protection built-in policy to EWH management group", "policyDefinitionId": "[variables('policyDefinitionIds').keyVaultDeletionProtection]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "StoragePubAccess-Assign", "properties": {"displayName": "Storage Account Public Access Assignment", "description": "Assignment of Storage Account Public Access built-in policy to EWH management group", "policyDefinitionId": "[variables('policyDefinitionIds').storagePublicAccess]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "EWH-NSGAssoc-Assign", "properties": {"displayName": "NSG Association Assignment", "description": "Assignment of NSG Association built-in policy to EWH management group", "policyDefinitionId": "[variables('policyDefinitionIds').nsgAssociation]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, {"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2021-06-01", "name": "EWH-PublicIP-Assign", "dependsOn": ["[resourceId('Microsoft.Authorization/policyDefinitions', 'EWH-PublicIPNotAllowed')]"], "properties": {"displayName": "Public IP Not Allowed Assignment", "description": "Assignment of Public IP Not Allowed custom policy to EWH management group", "policyDefinitionId": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/EWH-PublicIPNotAllowed')]", "scope": "[managementGroup().id]", "enforcementMode": "<PERSON><PERSON><PERSON>"}}]}