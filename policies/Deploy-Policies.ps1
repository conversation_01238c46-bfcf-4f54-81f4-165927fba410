# Deploy Azure Policies and Initiatives to Management Groups
# This script deploys custom and built-in policies to the EWH Landing Zone management groups

param(
    [Parameter(Mandatory = $true)]
    [string]$enterpriseScaleCompanyPrefix,
    
    [Parameter(Mandatory = $false)]
    [string]$templateFile = "deploy-policies.json",
    
    [Parameter(Mandatory = $false)]
    [string]$parametersFile = "deploy-policies.parameters.json",
    
    [Parameter(Mandatory = $false)]
    [string]$location = "East US"
)

# Set error action preference
$ErrorActionPreference = "Stop"

try {
    Write-Host "Starting Azure Policy deployment..." -ForegroundColor Green
    
    # Check if user is logged in to Azure
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Current Azure context: $($context.Account.Id)" -ForegroundColor Yellow
    
    # Deploy the ARM template at management group scope
    Write-Host "Deploying policies template..." -ForegroundColor Green

    $deploymentName = "EWH-Policies-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $managementGroupId = $enterpriseScaleCompanyPrefix

    $deployment = New-AzManagementGroupDeployment `
        -Name $deploymentName `
        -Location $location `
        -ManagementGroupId $managementGroupId `
        -TemplateFile $templateFile `
        -TemplateParameterFile $parametersFile `
        -enterpriseScaleCompanyPrefix $enterpriseScaleCompanyPrefix `
        -Verbose
    
    if ($deployment.ProvisioningState -eq "Succeeded") {
        Write-Host "Policy deployment completed successfully!" -ForegroundColor Green
        Write-Host "Deployment Name: $deploymentName" -ForegroundColor Yellow
    } else {
        Write-Host "Policy deployment failed with state: $($deployment.ProvisioningState)" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "Error occurred during deployment: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
