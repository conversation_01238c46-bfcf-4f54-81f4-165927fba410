{
  "mode": "All",
  "displayName": "Require mandatory tags on Resource Groups with system exclusions",
  "description": "This policy requires mandatory tags on Resource Groups while excluding Azure system-created Resource Groups",
    "parameters": {
      "owner": {
        "type": "String",
        "metadata": {
          "displayName": "Owner Tag",
          "description": "Email of responsible owner (must end with valid domain)"
        },
        "defaultValue": "owner"
      },
      "org": {
        "type": "String",
        "metadata": {
          "displayName": "Org Tag",
          "description": "Name of the organization"
        },
        "defaultValue": "org"
      },
      "created_by": {
        "type": "String",
        "metadata": {
          "displayName": "Created By Tag",
          "description": "Email of creator"
        },
        "defaultValue": "created_by"
      },
      "operation_team": {
        "type": "String",
        "metadata": {
          "displayName": "Operation Team Tag",
          "description": "Name of operational team"
        },
        "defaultValue": "operation_team"
      },
      "system_name": {
        "type": "String",
        "metadata": {
          "displayName": "System Name Tag",
          "description": "Name of the system/project"
        },
        "defaultValue": "system_name"
      },
      "app_name": {
        "type": "String",
        "metadata": {
          "displayName": "App Name Tag",
          "description": "Name of the application"
        },
        "defaultValue": "app_name"
      },
      "resource_type": {
        "type": "String",
        "metadata": {
          "displayName": "Resource Type Tag",
          "description": "Type of resource (Infrastructure, Application, Data, Security)"
        },
        "defaultValue": "resource_type"
      },
      "data_zone": {
        "type": "String",
        "metadata": {
          "displayName": "Data Zone Tag",
          "description": "Data zone classification (external/internal)"
        },
        "defaultValue": "data_zone"
      },
      "env": {
        "type": "String",
        "metadata": {
          "displayName": "Environment Tag",
          "description": "Environment (Production, Development, Testing, UAT)"
        },
        "defaultValue": "env"
      }
    },
    "policyRule": {
      "if": {
        "allOf": [
          {
            "field": "type",
            "equals": "Microsoft.Resources/subscriptions/resourceGroups"
          },
          {
            "field": "name",
            "notLike": "AzureBackupRG*"
          },
          {
            "field": "name",
            "notLike": "ResourceMover*"
          },
          {
            "field": "name",
            "notLike": "rg-cloudshell*"
          },
          {
            "field": "name",
            "notLike": "cloud-shell-storage-*"
          },
          {
            "field": "name",
            "notLike": "databricks-rg*"
          },
          {
            "field": "name",
            "notLike": "NetworkWatcherRG*"
          },
          {
            "field": "name",
            "notLike": "microsoft-network*"
          },
          {
            "field": "name",
            "notLike": "LogAnalyticsDefaultResources*"
          },
          {
            "field": "name",
            "notLike": "DynamicsDeployments*"
          },
          {
            "field": "name",
            "notLike": "MC_*"
          },
          {
            "field": "name",
            "notLike": "DefaultResourceGroup-*"
          },
          {
            "anyOf": [
              {
                "field": "[concat('tags[', parameters('org'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('env'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('created_by'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('operation_team'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('system_name'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('app_name'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('resource_type'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('data_zone'), ']')]",
                "exists": false
              },
              {
                "field": "[concat('tags[', parameters('owner'), ']')]",
                "exists": false
              }
            ]
          }
        ]
      },
      "then": {
        "effect": "deny"
      }
    }
  }
}
