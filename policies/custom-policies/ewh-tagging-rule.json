{"if": {"anyOf": [{"field": "tags['org']", "exists": false}, {"field": "tags['env']", "exists": false}, {"field": "tags['created_by']", "exists": false}, {"field": "tags['operation_team']", "exists": false}, {"field": "tags['system_name']", "exists": false}, {"field": "tags['app_name']", "exists": false}, {"field": "tags['resource_type']", "exists": false}, {"field": "tags['data_zone']", "exists": false}, {"allOf": [{"anyOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"field": "type", "like": "Microsoft.Compute/virtualMachines*"}, {"field": "type", "like": "Microsoft.Sql/*"}, {"field": "type", "like": "Microsoft.DBforMySQL/*"}, {"field": "type", "like": "Microsoft.DBforPostgreSQL/*"}, {"field": "type", "like": "Microsoft.DocumentDB/*"}, {"field": "type", "like": "Microsoft.Web/sites*"}, {"field": "type", "like": "Microsoft.Web/serverfarms*"}, {"field": "type", "equals": "Microsoft.Storage/storageAccounts"}]}, {"field": "tags['owner']", "exists": false}]}, {"allOf": [{"anyOf": [{"field": "type", "like": "Microsoft.Compute/virtualMachines*"}, {"field": "type", "like": "Microsoft.Sql/*"}, {"field": "type", "like": "Microsoft.DBforMySQL/*"}, {"field": "type", "like": "Microsoft.DBforPostgreSQL/*"}, {"field": "type", "like": "Microsoft.DocumentDB/*"}, {"field": "type", "like": "Microsoft.Web/sites*"}, {"field": "type", "like": "Microsoft.Web/serverfarms*"}, {"field": "type", "equals": "Microsoft.Storage/storageAccounts"}]}, {"field": "tags['priority']", "exists": false}]}]}, "then": {"effect": "deny"}}