{"if": {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions/resourceGroups"}, {"field": "name", "notLike": "AzureBackupRG*"}, {"field": "name", "notLike": "ResourceMover*"}, {"field": "name", "notLike": "rg-cloudshell*"}, {"field": "name", "notLike": "cloud-shell-storage-*"}, {"field": "name", "notLike": "databricks-rg*"}, {"field": "name", "notLike": "NetworkWatcherRG*"}, {"field": "name", "notLike": "microsoft-network*"}, {"field": "name", "notLike": "LogAnalyticsDefaultResources*"}, {"field": "name", "notLike": "DynamicsDeployments*"}, {"field": "name", "notLike": "MC_*"}, {"field": "name", "notLike": "DefaultResourceGroup-*"}, {"anyOf": [{"field": "[concat('tags[', parameters('org'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('env'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('created_by'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('operation_team'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('system_name'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('app_name'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('resource_type'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('data_zone'), ']')]", "exists": false}, {"field": "[concat('tags[', parameters('owner'), ']')]", "exists": false}]}]}, "then": {"effect": "deny"}}