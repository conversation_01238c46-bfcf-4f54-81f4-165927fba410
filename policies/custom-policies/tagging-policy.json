{"properties": {"displayName": "Require mandatory tags on resources with specific rules", "policyType": "Custom", "mode": "Indexed", "metadata": {"version": "1.0.0", "category": "Tagging"}, "version": "1.0.0", "parameters": {"owner": {"type": "String", "metadata": {"displayName": "Owner <PERSON>", "description": "Email of responsible owner (must end with valid domain)"}, "defaultValue": "owner"}, "org": {"type": "String", "metadata": {"displayName": "Org Tag", "description": "Name of the organization"}, "defaultValue": "org"}, "created_by": {"type": "String", "metadata": {"displayName": "Created By Tag", "description": "Email of creator"}, "defaultValue": "create_by"}, "operation_team": {"type": "String", "metadata": {"displayName": "Operation Team Tag", "description": "Name of operational team"}, "defaultValue": "operation_team"}, "system_name": {"type": "String", "metadata": {"displayName": "Project Name Tag", "description": "Name of the project"}, "defaultValue": "system_name"}, "env": {"type": "String", "metadata": {"displayName": "Environment Tag", "description": "Environment name (Production, Development, Testing, UAT)"}, "defaultValue": "env"}, "app_name": {"type": "String", "metadata": {"displayName": "App Name Tag", "description": "Name of the application"}, "defaultValue": "app_name"}, "resource_type": {"type": "String", "metadata": {"displayName": "Resource Type Tag", "description": "Resource classification (Infrastructure, Application, Data, Security)"}, "defaultValue": "resource_type"}, "priority": {"type": "String", "metadata": {"displayName": "Priority Tag", "description": "Priority level (High, Medium, Low)"}, "allowedValues": ["1", "2", "3"], "defaultValue": "1"}, "data_zone": {"type": "String", "metadata": {"displayName": "Data Zone Tag", "description": "Data zone (Public, Internal, Confidential, Restricted)"}, "allowedValues": ["external", "internal"], "defaultValue": "internal"}}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "[concat('tags[', parameters('org'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('env'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('created_by'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('operation_team'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('system_name'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('app_name'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('resource_type'), ']')]", "exists": false}]}, {"allOf": [{"field": "[concat('tags[', parameters('data_zone'), ']')]", "exists": false}]}, {"allOf": [{"anyOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"field": "type", "like": "Microsoft.Compute/virtualMachines*"}, {"field": "type", "like": "Microsoft.Sql/*"}, {"field": "type", "like": "Microsoft.DBforMySQL/*"}, {"field": "type", "like": "Microsoft.DBforPostgreSQL/*"}, {"field": "type", "like": "Microsoft.DocumentDB/*"}, {"field": "type", "like": "Microsoft.Web/sites*"}, {"field": "type", "like": "Microsoft.Web/serverfarms*"}]}, {"field": "[concat('tags[', parameters('owner'), ']')]", "exists": false}]}, {"allOf": [{"anyOf": [{"field": "type", "like": "Microsoft.Compute/virtualMachines*"}, {"field": "type", "like": "Microsoft.Sql/*"}, {"field": "type", "like": "Microsoft.DBforMySQL/*"}, {"field": "type", "like": "Microsoft.DBforPostgreSQL/*"}, {"field": "type", "like": "Microsoft.DocumentDB/*"}, {"field": "type", "like": "Microsoft.Web/sites*"}, {"field": "type", "like": "Microsoft.Web/serverfarms*"}]}, {"field": "[concat('tags[', parameters('priority'), ']')]", "exists": false}]}]}, "then": {"effect": "audit"}}}}