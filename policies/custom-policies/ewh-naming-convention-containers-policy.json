{"properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Containers", "policyType": "Custom", "mode": "All", "metadata": {"version": "1.0.0", "createdBy": "0e11d61f-ee99-47a2-936c-59f4deef8625", "createdOn": "2025-09-05T09:58:36.7448552Z", "updatedBy": null, "updatedOn": null}, "version": "1.0.0", "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.ContainerService/managedClusters"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[3]), 'aks'))]", "equals": true}]}]}]}, "then": {"effect": "deny"}}, "versions": ["1.0.0"]}, "id": "/providers/Microsoft.Management/managementGroups/ewh/providers/Microsoft.Authorization/policyDefinitions/1c90c9de-2c64-4be7-9458-f3eff01a9030", "type": "Microsoft.Authorization/policyDefinitions", "name": "1c90c9de-2c64-4be7-9458-f3eff01a9030", "systemData": {"createdBy": "<EMAIL>", "createdByType": "User", "createdAt": "2025-09-05T09:58:36.6845257Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User", "lastModifiedAt": "2025-09-05T09:58:36.6845257Z"}}