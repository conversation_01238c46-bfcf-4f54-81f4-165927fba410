{"properties": {"displayName": "Block public IP assignment to VM Network Interfaces", "policyType": "Custom", "mode": "Indexed", "description": "This policy denies creating or updating Network Interfaces if they are configured with any public IPs. Public IPs allow internet resources to communicate inbound to Azure resources and outbound to the internet.", "metadata": {"version": "1.0.0", "category": "Network", "createdBy": "Your ID or Name", "createdOn": "2025-09-08T00:00:00Z"}, "parameters": {}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Network/networkInterfaces/ipconfigurations[*].publicIpAddress.id", "exists": true}]}, "then": {"effect": "deny"}}}}