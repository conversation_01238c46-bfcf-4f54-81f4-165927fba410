{"properties": {"displayName": "Enforce EWH Landing Zone Naming Convention", "policyType": "Custom", "mode": "All", "metadata": {"version": "1.0.0", "createdBy": "0e11d61f-ee99-47a2-936c-59f4deef8625", "createdOn": "2025-09-05T09:59:03.3882229Z", "updatedBy": null, "updatedOn": null}, "version": "1.0.0", "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Resources/resourceGroups"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[3]), 'rg'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[3]), 'vnet'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Compute/virtualMachines"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[3]), 'vm'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"anyOf": [{"value": "[not(contains(toLower(field('name')), 'stg'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Web/sites"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[3]), 'app'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.KeyVault/vaults"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[3]), 'kv'))]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'sub'))]", "equals": true}, {"value": "[not(equals(toLower(split(field('name'), '-')[1]), 'platform'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')), 4)]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Resources/subscriptions"}, {"anyOf": [{"value": "[not(equals(toLower(split(field('name'), '-')[0]), 'sub'))]", "equals": true}, {"value": "[not(equals(toLower(split(field('name'), '-')[1]), 'ewh'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')), 4)]", "equals": true}]}]}]}, "then": {"effect": "deny"}}, "versions": ["1.0.0"]}, "id": "/providers/Microsoft.Management/managementGroups/ewh/providers/Microsoft.Authorization/policyDefinitions/1c90c9de-2c64-4be7-9458-f3eff01a903c", "type": "Microsoft.Authorization/policyDefinitions", "name": "1c90c9de-2c64-4be7-9458-f3eff01a903c", "systemData": {"createdBy": "<EMAIL>", "createdByType": "User", "createdAt": "2025-09-05T09:59:03.325426Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User", "lastModifiedAt": "2025-09-05T09:59:03.325426Z"}}