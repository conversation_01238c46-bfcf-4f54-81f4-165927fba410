{"properties": {"displayName": "Enforce EWH Landing Zone Naming Convention - Databases", "policyType": "Custom", "mode": "All", "metadata": {"version": "1.0.0", "createdBy": "0e11d61f-ee99-47a2-936c-59f4deef8625", "createdOn": "2025-09-05T09:57:56.4730954Z", "updatedBy": null, "updatedOn": null}, "version": "1.0.0", "parameters": {}, "policyRule": {"if": {"anyOf": [{"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers"}, {"anyOf": [{"comment": "Azure SQL Database: <env>-<system>-sql-<region>-<id>", "value": "[not(equals(toLower(split(field('name'), '-')[2]), 'sql'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')), 5)]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.Sql/servers/databases"}, {"anyOf": [{"comment": "Azure SQL Database: <env>-<system>-sql-<region>-<id>", "value": "[not(equals(toLower(split(split(field('name'), '/')[1], '-')[2]), 'sql'))]", "equals": true}, {"value": "[less(length(split(split(field('name'), '/')[1], '-')), 5)]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/servers"}, {"anyOf": [{"comment": "Azure MySQL Database: <env>-<system>-mysql-<region>-<id>", "value": "[not(equals(toLower(split(field('name'), '-')[2]), 'mysql'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')), 5)]", "equals": true}]}]}, {"allOf": [{"field": "type", "equals": "Microsoft.DBforMySQL/flexibleServers"}, {"anyOf": [{"comment": "Azure MySQL Database: <env>-<system>-mysql-<region>-<id>", "value": "[not(equals(toLower(split(field('name'), '-')[2]), 'mysql'))]", "equals": true}, {"value": "[less(length(split(field('name'), '-')), 5)]", "equals": true}]}]}]}, "then": {"effect": "deny"}}, "versions": ["1.0.0"]}, "id": "/providers/Microsoft.Management/managementGroups/ewh/providers/Microsoft.Authorization/policyDefinitions/1c90c9de-2c64-4be7-9458-f3eff01a9023", "type": "Microsoft.Authorization/policyDefinitions", "name": "1c90c9de-2c64-4be7-9458-f3eff01a9023", "systemData": {"createdBy": "<EMAIL>", "createdByType": "User", "createdAt": "2025-09-05T09:57:56.3967406Z", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User", "lastModifiedAt": "2025-09-05T09:57:56.3967406Z"}}