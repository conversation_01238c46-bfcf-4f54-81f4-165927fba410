{"properties": {"displayName": "Block SSH/RDP access from Internet to Virtual Machines", "description": "This policy blocks SSH (port 22) and RDP (port 3389) access from Internet to Virtual Machines", "mode": "All", "policyType": "Custom", "metadata": {"category": "Network", "version": "1.0.0"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups/securityRules"}, {"allOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "in": ["22", "3389"]}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "equals": "*"}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "Internet"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "0.0.0.0/0"}]}]}]}, "then": {"effect": "[parameters('effect')]"}}}}