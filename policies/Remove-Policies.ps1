# Remove Azure Policies and Initiatives
# This script removes all policies and assignments created by the EWH Landing Zone deployment

param(
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf,
    
    [Parameter(Mandatory = $false)]
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "Azure Policy Cleanup Script" -ForegroundColor Yellow
if ($WhatIf) {
    Write-Host "Running in WhatIf mode - no changes will be made" -ForegroundColor Cyan
}

try {
    # Check if user is logged in to Azure
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }

    Write-Host "Current Azure context: $($context.Account.Id)" -ForegroundColor Yellow

    if (-not $Force -and -not $WhatIf) {
        $confirmation = Read-Host "This will remove all EWH policies and assignments. Are you sure? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Host "Operation cancelled" -ForegroundColor Yellow
            exit 0
        }
    }

    # Step 1: Remove policy assignments
    Write-Host "`nStep 1: Removing policy assignments..." -ForegroundColor Green
    
    $assignments = @(
        "EWH-NamingConvention-LZ",
        "EWH-TaggingPolicy-Root",
        "EWH-BlockSshRdp-LZ",
        "BuiltIn-BlockSshRdp-LZ",
        "AllowedLocations-Root",
        "SecureTransferStorage-LZ",
        "KeyVaultDeletionProtection-LZ",
        "StoragePublicAccess-LZ"
    )

    foreach ($assignmentName in $assignments) {
        try {
            $assignment = Get-AzPolicyAssignment -Name $assignmentName -ErrorAction SilentlyContinue
            if ($assignment) {
                if ($WhatIf) {
                    Write-Host "Would remove policy assignment: $assignmentName" -ForegroundColor Cyan
                } else {
                    Remove-AzPolicyAssignment -Id $assignment.ResourceId -Confirm:$false
                    Write-Host "✓ Removed policy assignment: $assignmentName" -ForegroundColor Green
                }
            } else {
                Write-Host "Policy assignment not found: $assignmentName" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "✗ Error removing policy assignment '$assignmentName': $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # Step 2: Remove policy initiative
    Write-Host "`nStep 2: Removing policy initiative..." -ForegroundColor Green
    
    try {
        $initiative = Get-AzPolicySetDefinition -Name "EWH-NamingConventionInitiative" -ErrorAction SilentlyContinue
        if ($initiative) {
            if ($WhatIf) {
                Write-Host "Would remove policy initiative: EWH-NamingConventionInitiative" -ForegroundColor Cyan
            } else {
                Remove-AzPolicySetDefinition -Id $initiative.ResourceId -Force
                Write-Host "✓ Removed policy initiative: EWH-NamingConventionInitiative" -ForegroundColor Green
            }
        } else {
            Write-Host "Policy initiative not found: EWH-NamingConventionInitiative" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "✗ Error removing policy initiative: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Step 3: Remove custom policy definitions
    Write-Host "`nStep 3: Removing custom policy definitions..." -ForegroundColor Green
    
    $customPolicies = @(
        "EWH-NamingConvention",
        "EWH-NamingConvention-Databases", 
        "EWH-NamingConvention-Containers",
        "EWH-TaggingPolicy",
        "EWH-BlockSshRdp"
    )

    foreach ($policyName in $customPolicies) {
        try {
            $policy = Get-AzPolicyDefinition -Name $policyName -ErrorAction SilentlyContinue
            if ($policy) {
                if ($WhatIf) {
                    Write-Host "Would remove policy definition: $policyName" -ForegroundColor Cyan
                } else {
                    Remove-AzPolicyDefinition -Id $policy.ResourceId -Force
                    Write-Host "✓ Removed policy definition: $policyName" -ForegroundColor Green
                }
            } else {
                Write-Host "Policy definition not found: $policyName" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "✗ Error removing policy definition '$policyName': $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    if ($WhatIf) {
        Write-Host "`nWhatIf mode completed - no actual changes were made" -ForegroundColor Cyan
    } else {
        Write-Host "`nPolicy cleanup completed!" -ForegroundColor Green
    }
}
catch {
    Write-Host "Error occurred during cleanup: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nNote: Built-in policies are not removed as they are managed by Microsoft" -ForegroundColor Yellow
