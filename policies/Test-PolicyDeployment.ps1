# Test Azure Policy Deployment
# This script validates the policy deployment and checks policy assignments

param(
    [Parameter(Mandatory = $true)]
    [string]$enterpriseScaleCompanyPrefix
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "Testing Azure Policy deployment for prefix: $enterpriseScaleCompanyPrefix" -ForegroundColor Green

try {
    # Check if user is logged in to Azure
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }

    Write-Host "Current Azure context: $($context.Account.Id)" -ForegroundColor Yellow

    # Test 1: Check if custom policy definitions exist
    Write-Host "`nTest 1: Checking custom policy definitions..." -ForegroundColor Cyan
    
    $customPolicies = @(
        "EWH-NamingConvention",
        "EWH-NamingConvention-Databases", 
        "EWH-NamingConvention-Containers",
        "EWH-TaggingPolicy",
        "EWH-BlockSshRdp"
    )

    foreach ($policyName in $customPolicies) {
        try {
            $policy = Get-AzPolicyDefinition -Name $policyName -ErrorAction SilentlyContinue
            if ($policy) {
                Write-Host "✓ Policy '$policyName' found" -ForegroundColor Green
            } else {
                Write-Host "✗ Policy '$policyName' not found" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "✗ Error checking policy '$policyName': $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # Test 2: Check if policy initiative exists
    Write-Host "`nTest 2: Checking policy initiative..." -ForegroundColor Cyan
    
    try {
        $initiative = Get-AzPolicySetDefinition -Name "EWH-NamingConventionInitiative" -ErrorAction SilentlyContinue
        if ($initiative) {
            Write-Host "✓ Initiative 'EWH-NamingConventionInitiative' found" -ForegroundColor Green
        } else {
            Write-Host "✗ Initiative 'EWH-NamingConventionInitiative' not found" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Error checking initiative: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Test 3: Check management groups exist
    Write-Host "`nTest 3: Checking management groups..." -ForegroundColor Cyan
    
    $mgGroups = @(
        $enterpriseScaleCompanyPrefix,
        "LandingZone"
    )

    foreach ($mgName in $mgGroups) {
        try {
            $mg = Get-AzManagementGroup -GroupName $mgName -ErrorAction SilentlyContinue
            if ($mg) {
                Write-Host "✓ Management Group '$mgName' found" -ForegroundColor Green
            } else {
                Write-Host "✗ Management Group '$mgName' not found" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "✗ Error checking management group '$mgName': $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # Test 4: Check policy assignments
    Write-Host "`nTest 4: Checking policy assignments..." -ForegroundColor Cyan
    
    $assignments = @(
        "EWH-NamingConvention-LZ",
        "EWH-TaggingPolicy-Root",
        "EWH-BlockSshRdp-LZ",
        "BuiltIn-BlockSshRdp-LZ",
        "AllowedLocations-Root",
        "SecureTransferStorage-LZ",
        "KeyVaultDeletionProtection-LZ",
        "StoragePublicAccess-LZ"
    )

    foreach ($assignmentName in $assignments) {
        try {
            $assignment = Get-AzPolicyAssignment -Name $assignmentName -ErrorAction SilentlyContinue
            if ($assignment) {
                Write-Host "✓ Policy Assignment '$assignmentName' found - Scope: $($assignment.Properties.Scope)" -ForegroundColor Green
            } else {
                Write-Host "✗ Policy Assignment '$assignmentName' not found" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "✗ Error checking policy assignment '$assignmentName': $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # Test 5: Validate template syntax
    Write-Host "`nTest 5: Validating template syntax..." -ForegroundColor Cyan
    
    try {
        $validation = Test-AzTenantDeployment -Location "East US" -TemplateFile "deploy-policies.json" -TemplateParameterFile "deploy-policies.parameters.json" -enterpriseScaleCompanyPrefix $enterpriseScaleCompanyPrefix
        
        if ($validation.Count -eq 0) {
            Write-Host "✓ Template validation passed" -ForegroundColor Green
        } else {
            Write-Host "✗ Template validation failed:" -ForegroundColor Red
            $validation | ForEach-Object { Write-Host "  - $($_.Message)" -ForegroundColor Red }
        }
    }
    catch {
        Write-Host "✗ Template validation error: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host "`nPolicy deployment test completed!" -ForegroundColor Green
}
catch {
    Write-Host "Error occurred during testing: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
