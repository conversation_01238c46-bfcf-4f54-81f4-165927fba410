# EWH Landing Zone Policies

This module creates and assigns Azure policies and initiatives to the EWH Landing Zone management groups.

## Overview

This template deploys the following policies:

### Custom Policies
1. **Enforce EWH Landing Zone Naming Convention** - General naming convention for Azure resources
2. **Enforce EWH Landing Zone Naming Convention - Databases** - Naming convention for database resources
3. **Enforce EWH Landing Zone Naming Convention - Containers** - Naming convention for container resources
4. **EWH Tagging Policy** - Enforces required tags on Azure resources
5. **Block SSH/RDP access from Internet to Virtual Machines** - Custom policy to block SSH/RDP from internet

### Built-in Policies
1. **Block SSH/RDP access from Internet to Virtual Machines** - Built-in policy
2. **Allowed Locations** - Restricts resource deployment to allowed Azure regions
3. **Secure transfer to storage accounts should be enabled** - Enforces HTTPS for storage accounts
4. **Key vaults should have deletion protection enabled** - Protects key vaults from accidental deletion
5. **Configure your Storage account public access to be disallowed** - Prevents public access to storage accounts

### Policy Initiative
- **EWH Landing Zone Naming Convention Initiative** - Groups all three naming convention policies together

## File Structure

```
policies/
├── Deploy-Policies.ps1                                    # PowerShell deployment script
├── deploy-policies.json                                   # Main ARM template
├── deploy-policies.parameters.json                        # Parameters file
├── README.md                                             # This file
└── custom-policies/                                      # Individual policy definitions (for reference)
    ├── ewh-naming-convention-policy.json
    ├── ewh-naming-convention-databases-policy.json
    ├── ewh-naming-convention-containers-policy.json
    ├── tagging-policy.json
    └── block-ssh-rdp-policy.json
```

## Prerequisites

1. Azure PowerShell module installed
2. Appropriate permissions to create policies and assignments at tenant/management group level
3. Management groups must already exist (deploy management-groups template first)

## Deployment

### Method 1: Using PowerShell Script

```powershell
# Navigate to the policies directory
cd policies

# Run the deployment script
.\Deploy-Policies.ps1 -enterpriseScaleCompanyPrefix "EWH"
```

### Method 2: Using Azure CLI

```bash
# Deploy the template
az deployment tenant create \
  --name "EWH-Policies-$(date +%Y%m%d-%H%M%S)" \
  --location "East US" \
  --template-file deploy-policies.json \
  --parameters @deploy-policies.parameters.json \
  --parameters enterpriseScaleCompanyPrefix="EWH"
```

### Method 3: Using Azure PowerShell

```powershell
# Deploy the template
New-AzTenantDeployment `
  -Name "EWH-Policies-$(Get-Date -Format 'yyyyMMdd-HHmmss')" `
  -Location "East US" `
  -TemplateFile "deploy-policies.json" `
  -TemplateParameterFile "deploy-policies.parameters.json" `
  -enterpriseScaleCompanyPrefix "EWH"
```

## Parameters

| Parameter | Type | Description | Default Value |
|-----------|------|-------------|---------------|
| `enterpriseScaleCompanyPrefix` | string | Prefix for management group hierarchy | Required |
| `allowedLocations` | array | List of allowed Azure regions | East US, East US 2, West US, West US 2 |
| `tagPolicyParameters` | object | Parameters for tagging policy | Environment, CostCenter, Owner, Project |

## Policy Assignments

The template assigns policies to the following management groups:

- **Root Management Group**: Allowed Locations, Tagging Policy
- **Landing Zone Management Group**: All other policies and initiatives

## Customization

### Adding New Policies

1. Add the policy definition to the `resources` section of `deploy-policies.json`
2. Add the policy assignment to assign it to the appropriate management group
3. Update the parameters file if needed

### Modifying Policy Effects

You can change the policy effects by modifying the parameters in the assignments:
- `Audit` - Log violations but allow deployment
- `Deny` - Block non-compliant deployments
- `Disabled` - Turn off the policy

### Custom Naming Convention Rules

The naming convention policies are currently placeholder implementations. You need to:

1. Define your specific naming patterns in the `policyRule` section
2. Add regex patterns or other validation logic
3. Update the policy descriptions and metadata

## Notes

- The custom policy definitions for naming conventions are currently empty placeholders
- You need to implement the actual naming convention rules based on your requirements
- All policies are initially set to "Audit" mode for testing
- Change to "Deny" mode once you've validated the policies work as expected

## Troubleshooting

1. **Permission Issues**: Ensure you have Owner or Policy Contributor role at the tenant level
2. **Management Group Not Found**: Verify management groups exist before deploying policies
3. **Policy Assignment Failures**: Check that the policy definitions were created successfully first

## Next Steps

After deployment:
1. Test the policies in audit mode
2. Implement the actual naming convention rules
3. Change policy effects to "Deny" for enforcement
4. Monitor policy compliance in Azure Policy dashboard
