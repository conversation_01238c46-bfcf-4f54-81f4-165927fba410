<#
.SYNOPSIS
    Deploy Azure Landing Zone Management Groups Module

.DESCRIPTION
    This script deploys the management group hierarchy for Azure Landing Zone.
    Supports both Full and Lite deployment modes.

.PARAMETER EnterpriseScaleCompanyPrefix
    Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy.

.PARAMETER Location
    Azure region for the deployment.

.PARAMETER EnableLite
    Enable Enterprise Scale Lite deployment mode (simplified management group structure).

.PARAMETER TemplateFile
    Path to the ARM template file. Defaults to deploy-management-groups.json in the same directory.

.PARAMETER ParametersFile
    Path to the parameters file. Defaults to deploy-management-groups.parameters.json in the same directory.

.PARAMETER WhatIf
    Perform a what-if deployment to preview changes without actually deploying.

.EXAMPLE
    .\Deploy-ManagementGroups.ps1 -EnterpriseScaleCompanyPrefix "ewh" -Location "East US"

.EXAMPLE
    .\Deploy-ManagementGroups.ps1 -EnterpriseScaleCompanyPrefix "ewh" -Location "East US" -EnableLite

.EXAMPLE
    .\Deploy-ManagementGroups.ps1 -EnterpriseScaleCompanyPrefix "ewh" -Location "East US" -WhatIf
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $true)]
    [string]$Location,

    [Parameter(Mandatory = $false)]
    [switch]$EnableLite,

    [Parameter(Mandatory = $false)]
    [string]$TemplateFile,

    [Parameter(Mandatory = $false)]
    [string]$ParametersFile,

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Import required modules
try {
    Import-Module Az.Accounts -Force
    Import-Module Az.Resources -Force
}
catch {
    Write-Error "Failed to import required Azure PowerShell modules. Please ensure Az.Accounts and Az.Resources are installed."
    exit 1
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Set default file paths if not provided
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
if (-not $TemplateFile) {
    $TemplateFile = Join-Path $scriptPath "deploy-management-groups.json"
}
if (-not $ParametersFile) {
    $ParametersFile = Join-Path $scriptPath "deploy-management-groups.parameters.json"
}

# Validate template file exists
if (-not (Test-Path $TemplateFile)) {
    Write-Error "Template file not found: $TemplateFile"
    exit 1
}

# Validate parameters file exists
if (-not (Test-Path $ParametersFile)) {
    Write-Error "Parameters file not found: $ParametersFile"
    exit 1
}

# Generate deployment name
$deploymentName = "alz-mgmt-groups-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

# Prepare deployment parameters
$deploymentParameters = @{
    Name                = $deploymentName
    Location            = $Location
    TemplateFile        = $TemplateFile
    TemplateParameterFile = $ParametersFile
    enterpriseScaleCompanyPrefix = $EnterpriseScaleCompanyPrefix
    enableLite          = $EnableLite.IsPresent
}

Write-Host "`nDeployment Configuration:" -ForegroundColor Green
Write-Host "  Deployment Name: $deploymentName" -ForegroundColor White
Write-Host "  Company Prefix: $EnterpriseScaleCompanyPrefix" -ForegroundColor White
Write-Host "  Enable Lite Mode: $($EnableLite.IsPresent)" -ForegroundColor White
Write-Host "  Template File: $TemplateFile" -ForegroundColor White
Write-Host "  Parameters File: $ParametersFile" -ForegroundColor White
Write-Host "  Location: $Location" -ForegroundColor White

if ($WhatIf) {
    Write-Host "`nPerforming What-If deployment..." -ForegroundColor Yellow
    try {
        $whatIfResult = New-AzTenantDeployment @deploymentParameters -WhatIf
        Write-Host "`nWhat-If deployment completed successfully!" -ForegroundColor Green
        return $whatIfResult
    }
    catch {
        Write-Error "What-If deployment failed: $($_.Exception.Message)"
        exit 1
    }
}
else {
    Write-Host "`nStarting deployment..." -ForegroundColor Yellow
    try {
        $deployment = New-AzTenantDeployment @deploymentParameters
        
        if ($deployment.ProvisioningState -eq "Succeeded") {
            Write-Host "`nDeployment completed successfully!" -ForegroundColor Green
            Write-Host "  Deployment Name: $($deployment.DeploymentName)" -ForegroundColor White
            Write-Host "  Provisioning State: $($deployment.ProvisioningState)" -ForegroundColor White
            
            # Display created management groups
            Write-Host "`nCreated Management Groups:" -ForegroundColor Green
            if ($EnableLite) {
                Write-Host "  - $EnterpriseScaleCompanyPrefix (Root)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-platform (Platform)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-landingzones (Landing Zones)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-corp (Corp)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-online (Online)" -ForegroundColor White
            }
            else {
                Write-Host "  - $EnterpriseScaleCompanyPrefix (Root)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-platform (Platform)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-management (Management)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-connectivity (Connectivity)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-identity (Identity)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-landingzones (Landing Zones)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-corp (Corp)" -ForegroundColor White
                Write-Host "  - $EnterpriseScaleCompanyPrefix-online (Online)" -ForegroundColor White
            }
            Write-Host "  - $EnterpriseScaleCompanyPrefix-decommissioned (Decommissioned)" -ForegroundColor White
            Write-Host "  - $EnterpriseScaleCompanyPrefix-sandboxes (Sandboxes)" -ForegroundColor White
            
            return $deployment
        }
        else {
            Write-Error "Deployment failed with state: $($deployment.ProvisioningState)"
            exit 1
        }
    }
    catch {
        Write-Error "Deployment failed: $($_.Exception.Message)"
        exit 1
    }
}
