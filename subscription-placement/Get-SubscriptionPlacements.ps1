<#
.SYNOPSIS
    Get Subscription Placements in Management Groups

.DESCRIPTION
    This script retrieves and displays subscription placements across management groups.
    Supports filtering by management group and exporting results.

.PARAMETER ManagementGroupId
    Filter results to a specific management group. If not specified, shows all management groups.

.PARAMETER EnterpriseScaleCompanyPrefix
    Company prefix to filter management groups (e.g., "ewh").

.PARAMETER ExportPath
    Path to export results to CSV file.

.PARAMETER ShowEmpty
    Include management groups that have no subscriptions.

.EXAMPLE
    .\Get-SubscriptionPlacements.ps1

.EXAMPLE
    .\Get-SubscriptionPlacements.ps1 -ManagementGroupId "ewh-Platform-Management"

.EXAMPLE
    .\Get-SubscriptionPlacements.ps1 -EnterpriseScaleCompanyPrefix "ewh" -ExportPath "C:\temp\subscriptions.csv"

#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupId,

    [Parameter(Mandatory = $false)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [string]$ExportPath,

    [Parameter(Mandatory = $false)]
    [switch]$ShowEmpty
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Import required modules
try {
    Import-Module Az.Accounts -Force
    Import-Module Az.Resources -Force
}
catch {
    Write-Error "Failed to import required Azure PowerShell modules. Please ensure Az.Accounts and Az.Resources are installed."
    exit 1
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Function to get management group hierarchy with subscriptions
function Get-ManagementGroupHierarchy {
    param(
        [string]$GroupId,
        [int]$Level = 0
    )
    
    $results = @()
    
    try {
        if ($GroupId) {
            $mg = Get-AzManagementGroup -GroupId $GroupId -Expand
        }
        else {
            # Get root management groups
            $rootMgs = Get-AzManagementGroup
            foreach ($rootMg in $rootMgs) {
                $results += Get-ManagementGroupHierarchy -GroupId $rootMg.Name -Level $Level
            }
            return $results
        }
        
        # Process current management group
        $mgInfo = [PSCustomObject]@{
            ManagementGroupId = $mg.Name
            DisplayName = $mg.DisplayName
            Level = $Level
            Type = "ManagementGroup"
            SubscriptionId = $null
            SubscriptionName = $null
            SubscriptionState = $null
        }
        
        if ($ShowEmpty -or ($mg.Children -and ($mg.Children | Where-Object { $_.Type -eq "/subscriptions" }))) {
            $results += $mgInfo
        }
        
        # Process subscriptions in this management group
        if ($mg.Children) {
            foreach ($child in $mg.Children) {
                if ($child.Type -eq "/subscriptions") {
                    try {
                        $subscription = Get-AzSubscription -SubscriptionId $child.Name -ErrorAction SilentlyContinue
                        $subInfo = [PSCustomObject]@{
                            ManagementGroupId = $mg.Name
                            DisplayName = $mg.DisplayName
                            Level = $Level + 1
                            Type = "Subscription"
                            SubscriptionId = $child.Name
                            SubscriptionName = if ($subscription) { $subscription.Name } else { "Unknown" }
                            SubscriptionState = if ($subscription) { $subscription.State } else { "Unknown" }
                        }
                        $results += $subInfo
                    }
                    catch {
                        Write-Warning "Failed to get details for subscription $($child.Name)"
                    }
                }
                elseif ($child.Type -eq "/providers/Microsoft.Management/managementGroups") {
                    $results += Get-ManagementGroupHierarchy -GroupId $child.Name -Level ($Level + 1)
                }
            }
        }
    }
    catch {
        Write-Warning "Failed to process management group $GroupId : $($_.Exception.Message)"
    }
    
    return $results
}

# Function to filter results by company prefix
function Filter-ByCompanyPrefix {
    param(
        [array]$Results,
        [string]$Prefix
    )
    
    if (-not $Prefix) {
        return $Results
    }
    
    return $Results | Where-Object { 
        $_.ManagementGroupId -like "$Prefix*" -or 
        $_.ManagementGroupId -eq $Prefix 
    }
}

# Function to display results in a formatted table
function Show-Results {
    param([array]$Results)
    
    Write-Host "`nSubscription Placements:" -ForegroundColor Green
    Write-Host "=" * 80 -ForegroundColor Gray
    
    $currentMg = ""
    foreach ($result in $Results | Sort-Object ManagementGroupId, Type) {
        if ($result.ManagementGroupId -ne $currentMg) {
            $currentMg = $result.ManagementGroupId
            $indent = "  " * $result.Level
            Write-Host "`n$indent📁 $($result.DisplayName) ($($result.ManagementGroupId))" -ForegroundColor Cyan
        }
        
        if ($result.Type -eq "Subscription") {
            $indent = "  " * $result.Level
            $stateColor = switch ($result.SubscriptionState) {
                "Enabled" { "Green" }
                "Disabled" { "Red" }
                "Warned" { "Yellow" }
                default { "White" }
            }
            Write-Host "$indent  📋 $($result.SubscriptionName) ($($result.SubscriptionId)) - " -NoNewline -ForegroundColor White
            Write-Host "$($result.SubscriptionState)" -ForegroundColor $stateColor
        }
    }
    
    # Summary
    $totalMgs = ($Results | Where-Object { $_.Type -eq "ManagementGroup" }).Count
    $totalSubs = ($Results | Where-Object { $_.Type -eq "Subscription" }).Count
    
    Write-Host "`n" + "=" * 80 -ForegroundColor Gray
    Write-Host "Summary: $totalMgs Management Groups, $totalSubs Subscriptions" -ForegroundColor Green
}

# Main execution
Write-Host "`nRetrieving subscription placements..." -ForegroundColor Yellow

try {
    # Get management group hierarchy
    if ($ManagementGroupId) {
        $results = Get-ManagementGroupHierarchy -GroupId $ManagementGroupId
    }
    else {
        $results = Get-ManagementGroupHierarchy
    }
    
    # Filter by company prefix if specified
    if ($EnterpriseScaleCompanyPrefix) {
        $results = Filter-ByCompanyPrefix -Results $results -Prefix $EnterpriseScaleCompanyPrefix
    }
    
    # Display results
    if ($results) {
        Show-Results -Results $results
        
        # Export to CSV if requested
        if ($ExportPath) {
            $results | Export-Csv -Path $ExportPath -NoTypeInformation
            Write-Host "`n📄 Results exported to: $ExportPath" -ForegroundColor Green
        }
    }
    else {
        Write-Host "`n⚠️ No subscription placements found matching the criteria." -ForegroundColor Yellow
    }
}
catch {
    Write-Error "Failed to retrieve subscription placements: $($_.Exception.Message)"
    exit 1
}

Write-Host "`n🎉 Operation completed successfully!" -ForegroundColor Green
