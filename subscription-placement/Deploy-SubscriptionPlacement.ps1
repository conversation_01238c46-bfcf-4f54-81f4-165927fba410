<#
.SYNOPSIS
    Deploy Subscription Placement to Management Groups

.DESCRIPTION
    This script places one or more subscriptions into specific management groups.
    Supports both single subscription placement and bulk operations.

.PARAMETER SubscriptionId
    The subscription ID to be placed in the management group.

.PARAMETER TargetManagementGroupId
    The management group ID where the subscription should be placed.

.PARAMETER SubscriptionMappings
    Array of subscription mappings for bulk operations. Each mapping should have SubscriptionId and TargetManagementGroupId properties.

.PARAMETER TemplateFile
    Path to the ARM template file. Defaults to subscriptionOrganization.json in the same directory.

.PARAMETER Location
    Azure region for the deployment. Defaults to "East US".

.PARAMETER WhatIf
    Perform a What-If deployment to preview changes without making them.

.EXAMPLE
    .\Deploy-SubscriptionPlacement.ps1 -SubscriptionId "12345678-1234-1234-1234-123456789012" -TargetManagementGroupId "ewh-Platform-Management"

.EXAMPLE
    $mappings = @(
        @{ SubscriptionId = "12345678-1234-1234-1234-123456789012"; TargetManagementGroupId = "ewh-Platform-Management" },
        @{ SubscriptionId = "87654321-4321-4321-4321-210987654321"; TargetManagementGroupId = "ewh-Platform-Connectivity" }
    )
    .\Deploy-SubscriptionPlacement.ps1 -SubscriptionMappings $mappings

#>

[CmdletBinding(DefaultParameterSetName = 'Single')]
param(
    [Parameter(Mandatory = $true, ParameterSetName = 'Single')]
    [ValidatePattern('^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$')]
    [string]$SubscriptionId,

    [Parameter(Mandatory = $true, ParameterSetName = 'Single')]
    [string]$TargetManagementGroupId,

    [Parameter(Mandatory = $true, ParameterSetName = 'Bulk')]
    [array]$SubscriptionMappings,

    [Parameter(Mandatory = $false)]
    [string]$TemplateFile,

    [Parameter(Mandatory = $false)]
    [string]$Location = "East US",

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Import required modules
try {
    Import-Module Az.Accounts -Force
    Import-Module Az.Resources -Force
}
catch {
    Write-Error "Failed to import required Azure PowerShell modules. Please ensure Az.Accounts and Az.Resources are installed."
    exit 1
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Set default template file path if not provided
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
if (-not $TemplateFile) {
    $TemplateFile = Join-Path $scriptPath "subscriptionOrganization.json"
}

# Validate template file exists
if (-not (Test-Path $TemplateFile)) {
    Write-Error "Template file not found: $TemplateFile"
    exit 1
}

# Prepare subscription mappings
if ($PSCmdlet.ParameterSetName -eq 'Single') {
    $SubscriptionMappings = @(
        @{
            SubscriptionId = $SubscriptionId
            TargetManagementGroupId = $TargetManagementGroupId
        }
    )
}

Write-Host "`nSubscription Placement Configuration:" -ForegroundColor Green
foreach ($mapping in $SubscriptionMappings) {
    Write-Host "  Subscription: $($mapping.SubscriptionId) -> Management Group: $($mapping.TargetManagementGroupId)" -ForegroundColor White
}

# Function to deploy subscription placement
function Deploy-SubscriptionPlacement {
    param(
        [string]$SubId,
        [string]$MgId,
        [string]$Template,
        [string]$DeployLocation,
        [bool]$IsWhatIf
    )

    try {
        if ($IsWhatIf) {
            Write-Host "`nWhat-If: Would place subscription $SubId in management group $MgId" -ForegroundColor Yellow

            # Check if management group exists
            $mg = Get-AzManagementGroup -GroupId $MgId -ErrorAction SilentlyContinue
            if (-not $mg) {
                Write-Warning "Management group '$MgId' does not exist"
                return $null
            }

            # Check if subscription exists
            $sub = Get-AzSubscription -SubscriptionId $SubId -ErrorAction SilentlyContinue
            if (-not $sub) {
                Write-Warning "Subscription '$SubId' does not exist or is not accessible"
                return $null
            }

            Write-Host "What-If: Subscription '$($sub.Name)' would be placed in management group '$($mg.DisplayName)'" -ForegroundColor Green
            return @{ Status = "WhatIf"; SubscriptionId = $SubId; ManagementGroupId = $MgId }
        }
        else {
            Write-Host "`nPlacing subscription $SubId in management group $MgId..." -ForegroundColor Yellow

            # Use PowerShell command to move subscription
            $result = New-AzManagementGroupSubscription -GroupId $MgId -SubscriptionId $SubId

            Write-Host "Successfully placed subscription $SubId in management group $MgId" -ForegroundColor Green
            return $result
        }
    }
    catch {
        Write-Error "Failed to place subscription $SubId in management group $MgId : $($_.Exception.Message)"
        throw
    }
}

# Deploy subscription placements
$results = @()
foreach ($mapping in $SubscriptionMappings) {
    try {
        $result = Deploy-SubscriptionPlacement -SubId $mapping.SubscriptionId -MgId $mapping.TargetManagementGroupId -Template $TemplateFile -DeployLocation $Location -IsWhatIf $WhatIf.IsPresent
        $results += $result
    }
    catch {
        Write-Error "Failed to process subscription $($mapping.SubscriptionId): $($_.Exception.Message)"
        continue
    }
}

if (-not $WhatIf) {
    Write-Host "`nSubscription placement deployment completed!" -ForegroundColor Green
    Write-Host "Processed $($SubscriptionMappings.Count) subscription(s)" -ForegroundColor White
}
else {
    Write-Host "`nWhat-If deployment completed!" -ForegroundColor Green
    Write-Host "Reviewed $($SubscriptionMappings.Count) subscription placement(s)" -ForegroundColor White
}

return $results
