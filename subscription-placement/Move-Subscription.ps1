<#
.SYNOPSIS
    Move Subscription Between Management Groups

.DESCRIPTION
    This script moves a subscription from one management group to another.
    Includes validation and rollback capabilities.

.PARAMETER SubscriptionId
    The subscription ID to be moved.

.PARAMETER SourceManagementGroupId
    The current management group ID where the subscription is located.

.PARAMETER TargetManagementGroupId
    The target management group ID where the subscription should be moved.

.PARAMETER Force
    Skip confirmation prompts.

.PARAMETER WhatIf
    Perform a What-If operation to preview changes without making them.

.EXAMPLE
    .\Move-Subscription.ps1 -SubscriptionId "12345678-1234-1234-1234-123456789012" -SourceManagementGroupId "ewh-Platform-Management" -TargetManagementGroupId "ewh-Platform-Connectivity"

.EXAMPLE
    .\Move-Subscription.ps1 -SubscriptionId "12345678-1234-1234-1234-123456789012" -SourceManagementGroupId "ewh-Platform-Management" -TargetManagementGroupId "ewh-Platform-Connectivity" -Force

#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidatePattern('^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$')]
    [string]$SubscriptionId,

    [Parameter(Mandatory = $true)]
    [string]$SourceManagementGroupId,

    [Parameter(Mandatory = $true)]
    [string]$TargetManagementGroupId,

    [Parameter(Mandatory = $false)]
    [switch]$Force,

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Import required modules
try {
    Import-Module Az.Accounts -Force
    Import-Module Az.Resources -Force
}
catch {
    Write-Error "Failed to import required Azure PowerShell modules. Please ensure Az.Accounts and Az.Resources are installed."
    exit 1
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Function to get subscription details
function Get-SubscriptionDetails {
    param([string]$SubId)
    
    try {
        $subscription = Get-AzSubscription -SubscriptionId $SubId
        return $subscription
    }
    catch {
        Write-Error "Failed to get subscription details for $SubId : $($_.Exception.Message)"
        return $null
    }
}

# Function to verify management group exists
function Test-ManagementGroupExists {
    param([string]$MgId)
    
    try {
        $mg = Get-AzManagementGroup -GroupId $MgId -ErrorAction SilentlyContinue
        return $mg -ne $null
    }
    catch {
        return $false
    }
}

# Function to get current management group of subscription
function Get-SubscriptionManagementGroup {
    param([string]$SubId)
    
    try {
        # Get all management groups and find the one containing this subscription
        $allMgs = Get-AzManagementGroup -Expand
        foreach ($mg in $allMgs) {
            if ($mg.Children) {
                foreach ($child in $mg.Children) {
                    if ($child.Type -eq "/subscriptions" -and $child.Name -eq $SubId) {
                        return $mg.Name
                    }
                }
            }
        }
        return $null
    }
    catch {
        Write-Warning "Failed to determine current management group for subscription $SubId"
        return $null
    }
}

# Validate inputs
Write-Host "`nValidating inputs..." -ForegroundColor Yellow

# Get subscription details
$subscription = Get-SubscriptionDetails -SubId $SubscriptionId
if (-not $subscription) {
    Write-Error "Subscription $SubscriptionId not found or not accessible."
    exit 1
}

Write-Host "✅ Subscription found: $($subscription.Name) ($($subscription.Id))" -ForegroundColor Green

# Verify source management group exists
if (-not (Test-ManagementGroupExists -MgId $SourceManagementGroupId)) {
    Write-Error "Source management group '$SourceManagementGroupId' not found."
    exit 1
}

Write-Host "✅ Source management group exists: $SourceManagementGroupId" -ForegroundColor Green

# Verify target management group exists
if (-not (Test-ManagementGroupExists -MgId $TargetManagementGroupId)) {
    Write-Error "Target management group '$TargetManagementGroupId' not found."
    exit 1
}

Write-Host "✅ Target management group exists: $TargetManagementGroupId" -ForegroundColor Green

# Check if subscription is currently in the source management group
$currentMg = Get-SubscriptionManagementGroup -SubId $SubscriptionId
if ($currentMg -and $currentMg -ne $SourceManagementGroupId) {
    Write-Warning "⚠️ Subscription is currently in management group '$currentMg', not '$SourceManagementGroupId'"
    if (-not $Force) {
        $continue = Read-Host "Do you want to continue with the move from '$currentMg' to '$TargetManagementGroupId'? (y/N)"
        if ($continue -ne 'y' -and $continue -ne 'Y') {
            Write-Host "Operation cancelled by user." -ForegroundColor Yellow
            exit 0
        }
    }
    $SourceManagementGroupId = $currentMg
}

# Display move summary
Write-Host "`nMove Summary:" -ForegroundColor Cyan
Write-Host "  Subscription: $($subscription.Name) ($SubscriptionId)" -ForegroundColor White
Write-Host "  From: $SourceManagementGroupId" -ForegroundColor White
Write-Host "  To: $TargetManagementGroupId" -ForegroundColor White

if ($WhatIf) {
    Write-Host "`n📋 What-If Mode: No actual changes will be made" -ForegroundColor Yellow
}
else {
    if (-not $Force) {
        $confirm = Read-Host "`nDo you want to proceed with this move? (y/N)"
        if ($confirm -ne 'y' -and $confirm -ne 'Y') {
            Write-Host "Operation cancelled by user." -ForegroundColor Yellow
            exit 0
        }
    }
}

# Perform the move
try {
    if ($WhatIf) {
        Write-Host "`n📋 What-If: Would move subscription $SubscriptionId from $SourceManagementGroupId to $TargetManagementGroupId" -ForegroundColor Yellow
    }
    else {
        Write-Host "`nMoving subscription..." -ForegroundColor Yellow
        
        # Use the subscription placement script
        $scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
        $placementScript = Join-Path $scriptPath "Deploy-SubscriptionPlacement.ps1"
        
        if (Test-Path $placementScript) {
            & $placementScript -SubscriptionId $SubscriptionId -TargetManagementGroupId $TargetManagementGroupId
        }
        else {
            # Fallback to direct PowerShell command
            New-AzManagementGroupSubscription -GroupId $TargetManagementGroupId -SubscriptionId $SubscriptionId
        }
        
        Write-Host "✅ Successfully moved subscription $SubscriptionId to management group $TargetManagementGroupId" -ForegroundColor Green
    }
}
catch {
    Write-Error "❌ Failed to move subscription: $($_.Exception.Message)"
    
    # Attempt rollback if needed
    if (-not $WhatIf) {
        Write-Host "`nAttempting rollback..." -ForegroundColor Yellow
        try {
            New-AzManagementGroupSubscription -GroupId $SourceManagementGroupId -SubscriptionId $SubscriptionId
            Write-Host "✅ Rollback successful - subscription returned to $SourceManagementGroupId" -ForegroundColor Green
        }
        catch {
            Write-Error "❌ Rollback failed: $($_.Exception.Message)"
        }
    }
    exit 1
}

Write-Host "`n🎉 Move operation completed successfully!" -ForegroundColor Green
