{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"enterpriseScaleCompanyPrefix": {"value": null}, "telemetryOptOut": {"value": "No"}, "platformManagementGroup": {"value": null}, "IdentityManagementGroup": {"value": null}, "managementManagementGroup": {"value": null}, "connectivityManagementGroup": {"value": null}, "LandingZoneManagementGroup": {"value": "contoso-landingzones"}, "enableAMBAConnectivity": {"value": "Yes"}, "enableAMBAIdentity": {"value": "Yes"}, "enableAMBAManagement": {"value": "Yes"}, "enableAMBAServiceHealth": {"value": "Yes"}, "enableAMBANotificationAssets": {"value": "Yes"}, "enableAMBAHybridVM": {"value": "Yes"}, "enableAMBAKeyManagement": {"value": "Yes"}, "enableAMBALoadBalancing": {"value": "Yes"}, "enableAMBANetworkChanges": {"value": "Yes"}, "enableAMBARecoveryServices": {"value": "Yes"}, "enableAMBAStorage": {"value": "Yes"}, "enableAMBAVM": {"value": "Yes"}, "enableAMBAWeb": {"value": "Yes"}, "bringYourOwnUserAssignedManagedIdentity": {"value": "No"}, "bringYourOwnUserAssignedManagedIdentityResourceId": {"value": null}, "userAssignedManagedIdentityName": {"value": "id-amba-prod-001"}, "managementSubscriptionId": {"value": null}, "ALZMonitorResourceGroupName": {"value": "rg-amba-monitoring-001"}, "ALZMonitorResourceGroupLocation": {"value": "eastasia"}, "ALZMonitorResourceGroupTags": {"value": null}, "ALZMonitorDisableTagName": {"value": null}, "ALZMonitorDisableTagValues": {"value": null}, "ALZMonitorActionGroupEmail": {"value": null}, "ALZLogicappResourceId": {"value": null}, "ALZLogicappCallbackUrl": {"value": null}, "ALZArmRoleId": {"value": null}, "ALZEventHubResourceId": {"value": null}, "ALZWebhookServiceUri": {"value": null}, "ALZFunctionResourceId": {"value": null}, "ALZFunctionTriggerUrl": {"value": null}, "BYOActionGroup": {"value": null}, "BYOAlertProcessingRule": {"value": null}, "ALZAlertSeverity": {"value": null}, "ALZNotificationAssetSuffix": {"value": null}, "currentDateTimeUtcNow": {"value": null}, "policyAssignmentExclusionList": {"value": null}, "policyAssignmentParametersConnectivity": {"value": null}, "policyAssignmentParametersConnectivity_2": {"value": null}, "policyAssignmentParametersIdentity": {"value": null}, "policyAssignmentParametersHybridVM": {"value": null}, "policyAssignmentParametersManagement": {"value": null}, "policyAssignmentParametersServiceHealth": {"value": null}, "policyAssignmentParametersKeyManagement": {"value": null}, "policyAssignmentParametersLoadBalancing": {"value": null}, "policyAssignmentParametersNetworkChanges": {"value": null}, "policyAssignmentParametersRecoveryServices": {"value": null}, "policyAssignmentParametersStorage": {"value": null}, "policyAssignmentParametersVM": {"value": null}, "policyAssignmentParametersWeb": {"value": null}, "deployAMBAPortalAccelerator": {"value": "Yes"}, "deployALZPortalAccelerator": {"value": null}}}