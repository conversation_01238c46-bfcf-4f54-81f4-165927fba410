# Simple script to test Management Group access
param(
    [string]$ManagementGroupPrefix = "demo-ewh"
)

Write-Host "Testing Management Group Access for: $ManagementGroupPrefix" -ForegroundColor Green

# Import required modules
try {
    Import-Module Az.Accounts -Force
    Import-Module Az.Resources -Force
    Write-Host "Modules imported successfully" -ForegroundColor Green
}
catch {
    Write-Error "Failed to import modules: $($_.Exception.Message)"
    exit 1
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Cyan
Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Try to get the management group
try {
    Write-Host "`nTrying to get Management Group: $ManagementGroupPrefix" -ForegroundColor Yellow
    $mg = Get-AzManagementGroup -GroupId $ManagementGroupPrefix -Expand -Recurse
    
    if ($mg) {
        Write-Host "SUCCESS: Found Management Group" -ForegroundColor Green
        Write-Host "  Name: $($mg.Name)" -ForegroundColor White
        Write-Host "  Display Name: $($mg.DisplayName)" -ForegroundColor White
        Write-Host "  Parent ID: $($mg.ParentId)" -ForegroundColor White
        
        if ($mg.Children) {
            Write-Host "  Children Count: $($mg.Children.Count)" -ForegroundColor White
            foreach ($child in $mg.Children) {
                Write-Host "    - $($child.DisplayName) ($($child.Name)) - Type: $($child.Type)" -ForegroundColor Gray
            }
        }
        
        # Try to get policy assignments
        Write-Host "`nTrying to get Policy Assignments..." -ForegroundColor Yellow
        $scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupPrefix"
        $assignments = Get-AzPolicyAssignment -Scope $scope
        
        if ($assignments) {
            Write-Host "Found $($assignments.Count) policy assignments:" -ForegroundColor Green
            foreach ($assignment in $assignments) {
                Write-Host "  - $($assignment.Properties.DisplayName)" -ForegroundColor White
            }
        }
        else {
            Write-Host "No policy assignments found" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "Management Group not found" -ForegroundColor Red
    }
}
catch {
    Write-Error "Error accessing Management Group: $($_.Exception.Message)"
    Write-Host "This could be due to:" -ForegroundColor Yellow
    Write-Host "1. Management Group does not exist" -ForegroundColor White
    Write-Host "2. Insufficient permissions" -ForegroundColor White
    Write-Host "3. Wrong management group name" -ForegroundColor White
}

Write-Host "`nTest completed!" -ForegroundColor Green
